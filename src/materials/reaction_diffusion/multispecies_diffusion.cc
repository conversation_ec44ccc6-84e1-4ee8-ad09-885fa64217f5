/*
    <PERSON> (<EMAIL>), 2023
*/

#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <fstream>
#include <iostream>
#include <algorithm>
#include <iterator>
#include <numeric>
#include <array>
#include <tuple>
#include <pyre/journal.h>
#include "../../mathlib/mathlib.h"
#include "../../restart/checkpoint.h"
#include "../../mathlib/mathvec.h"
#include "../../mathlib/mathmat.h"
#include "multispecies_diffusion.h"

summit::MultispeciesDiffusion::MultispeciesDiffusion(const std::string& name) : ReactionDiffusionMaterial(name, 1) { }

summit::MultispeciesDiffusion::~MultispeciesDiffusion() { }

void summit::MultispeciesDiffusion::Load(const std::string& filename, const std::string& line) {
    pyre::journal::firewall_t error("summit.materials.multispecies_diffusion");
    error << "Died in MultispeciesDiffusion Load()." << pyre::journal::newline
          << "This material does not support the .dat Load() method."
          << pyre::journal::endl(__HERE__);
    return; // Done!
}

#ifdef WITH_YAML_CPP
void summit::MultispeciesDiffusion::Load(const YAML::Node &yamlNode) {
    
    pyre::journal::error_t error("summit.materials.elastic.multispecies_diffusion");

    try { // Read material parameters
        _nsp   = yamlNode["number of species"].as<int>();
        _names = yamlNode["species names"].as<std::vector<std::string>>();
        _Dij   = yamlNode["binary interdiffusivites"].as<std::vector<std::vector<real>>>();
        _Mi    = yamlNode["molar masses"].as<std::vector<real>>();
        _p     = yamlNode["pressure"].as<real>();
        _T     = yamlNode["temperature"].as<real>();
        _type  = yamlNode["flux type"].as<std::string>();
    } catch (...) {
        error << "Error in MultispeciesDiffusion Material YAML reader: "
              << "A required key was not provided or cannot be cast to the right type."
              << pyre::journal::endl(__HERE__);
    }

    // Check vector lengths for compliance
    if (_names.size() != _nsp || _Dij.size() != _nsp || _Dij[0].size() != _nsp || _Mi.size() != _nsp)
        error << "Error in MultispeciesDiffusion Material : Expected vectors of size nsp = " << _nsp
              << pyre::journal::endl(__HERE__);

    // Check type for compliance
    if (_type != "Fickian" && _type != "Blottner-unconstrained" 
            && _type != "Blottner-constrained" && _type != "Blottner-explicit")
        error << "Error in MultispeciesDiffusion Material : The provided type is not recognized."
              << pyre::journal::endl(__HERE__);

    // Compute density
    _rho = _p / _R / _T;

    // Set internal variable fields
    _nInt = 1;
    this->_setInternalVariableMap();

    return; // Done!
}
#endif

void summit::MultispeciesDiffusion::_setInternalVariableMap() {
    SetLocationInInternalTable("FluxSum", 0, 3); // If in 2D, only 2 components are used
    return; // Done!
}

void summit::MultispeciesDiffusion::Display() {

    pyre::journal::info_t info("summit.materials.blottner");

    // General
    info << "------------------------------------------------------------"
         << pyre::journal::newline
         << "Material Model: multispecies diffusion with " << _type << " approximation"
         << pyre::journal::newline
         << "Pressure: .............................. " << _p << " Pa"
         << pyre::journal::newline
         << "Temperature: ........................... " << _T << " K"
         << pyre::journal::newline
         << "Number of species: ..................... " << _nsp
         << pyre::journal::newline
         << "Binary diffusion coefficient matrix (m^2/s):"
         << pyre::journal::newline << "\t";

    // Binary interdiffusivities
    for (int k = 0; k < _nsp; k++) info << "\t" << _names[k];
    info << pyre::journal::newline;
    for (int k = 0; k < _nsp; k++) {
        info << _names[k] << "\t";
        for (int l = 0; l < _nsp; l++)
            info << _Dij[k][l] << "\t";
        info << pyre::journal::newline;
    }

    // Molar masses
    info << "Molar masses (kg/kmol): [";
    for (int k = 0; k < _nsp; k++) info << _Mi[k] << ", ";
    info << "]" << pyre::journal::newline;
    
    info << "------------------------------------------------------------" 
         << pyre::journal::endl;

    return; // Done!
}

summit::real summit::MultispeciesDiffusion::diffusivity(const real* Fn,
                                            const real* q,
                                            const int ndm,
                                            const int component) const {
    return 0;
}

void summit::MultispeciesDiffusion::Constitutive(const real* Y0,
                                    const real* Y1,
                                    const real* gradY0,
                                    const real* gradY1,
                                    real* F,
                                    real* internal,
                                    real* dFdgradY,
                                    real* dFdY,
                                    real dt,
                                    const int ndf,
                                    const int ndm,
                                    bool compute_tangents,
                                    bool artVisc_interface_activate) const {
    
    real Mbar = this->Mbar(Y1); // Mean molar mass

    real sum_Fstar[ndm];
    std::fill(sum_Fstar, sum_Fstar + ndm, 0.);

    int fluxsum_position; size_t size;
    this->GetLocationInInternalTable("FluxSum", fluxsum_position, size);

    /**
     * Flux Type: Blottner diagonal approximation with constrained
     * (normalized) fluxes
     */
    if (_type == "Blottner-constrained") {

        // Precompute advectively cross-coupling sums
        real Di[_nsp], dDidYj[_nsp*_nsp], sum_dFdY[_nsp * ndm];
        std::fill(sum_dFdY, sum_dFdY + _nsp*ndm, 0.);
        std::fill(dDidYj, dDidYj + _nsp*_nsp, 0.);

        for (int i = 0; i < _nsp; i++) {
            // Compute Blottner interdiffusivities
            real num = 0, den = 0;
            for (int j = 0; j < _nsp; j++)
                if (i != j) {
                    num += Y1[j] / _Mi[j];
                    den += Y1[j] / _Mi[j] / _Dij[i][j];
                }
            Di[i] = num / den;

            // Loop over spatial dimensions
            for (int dim = 0; dim < ndm; dim++) {
                int id = dim + i * ndm;

                // Assemble non-normalized flux sum in each dimension
                sum_Fstar[dim] += _rho * Di[i] * gradY1[id];
                
                // Assemble non-normalized flux state derivative sum
                if (compute_tangents) {
                    for (int j = 0; j < _nsp; j++) {
                        int jd = j + _nsp * dim, ij = j + _nsp * i;
                        if (i != j) 
                            dDidYj[ij] = (1. / _Mi[j] / den) - (num / _Dij[i][j] / _Mi[j] / (den*den));
                        sum_dFdY[jd] += _rho * dDidYj[ij] * gradY1[id];
                    }
                }
            }
        }

        // Compute and save normalized fluxes
        for (int i = 0; i < _nsp; i++) {
            for (int dim = 0; dim < ndm; dim++) {
                int id = dim + i * ndm;
                real Fstar = _rho * Di[i] * gradY1[id]; // Flux in direction dim
                F[id] = Fstar - Y1[i] * sum_Fstar[dim]; // Normalized flux
                
                if (compute_tangents) {
                    for (int j = 0; j < _nsp; j++) {
                        // dFdY tangent
                        int idj = j + _nsp * id, ij = j + _nsp * i;
                        real dFstardY = _rho * dDidYj[ij] * gradY1[id];
                        dFdY[idj] = dFstardY - Y1[i] * sum_dFdY[id];
                        if (i == j) dFdY[idj] -= sum_Fstar[dim];

                        // dFdDY tangent 4-tensor - offdiagonal term
                        for (int k = 0; k < ndm; k++) {
                            int idjk = k + ndm * idj;
                            dFdgradY[idjk] = -Y1[i]* _rho * Di[j];
                        }
                    }
                    // dFdDY tangent 4-tensor - diagonal term
                    int idjk = id * (ndm * _nsp) + id;
                    dFdgradY[idjk] -= _rho * Di[i];
                }
            }
        }
    }

    /**
     * Flux Type: Blottner diagonal approximation with unconstrained
     * (unnormalized) fluxes
     */
    else if (_type == "Blottner-unconstrained" || _type == "Blottner-explicit") {
        for (int i = 0; i < _nsp; i++) {
            // Assemble Blottner interdiffusivity
            real num = 0, den = 0;
            for (int j = 0; j < _nsp; j++)
                {//if (i != j) {
                    num += Y1[j] / _Mi[j];
                    den += Y1[j] / _Mi[j] / _Dij[i][j];
                }
            real Di = num / den;

            // Loop over spatial dimensions
            for (int dim = 0; dim < ndm; dim++) {
                int id = dim + i * ndm;
                F[id] = _rho * Di * gradY1[id]; // Flux in direction dim
                sum_Fstar[dim] += F[id];

                if (_type == "Blottner-explicit") // Normalize flux explicitly
                    F[id] -= Y0[i] * internal[fluxsum_position + dim];
                
                if (compute_tangents) {
                    // dFdY tangent
                    for (int j = 0; j < _nsp; j++) {
                        int idj = j + _nsp * id;
                        real dDidYj = 0.;
                        //if (i != j) 
                            dDidYj = (1. / _Mi[j] / den) - (num / _Dij[i][j] / _Mi[j] / (den*den));
                        dFdY[idj] = _rho * dDidYj * gradY1[id];
                    }
                    // dFdDY tangent
                    int idjk = id * (ndm * _nsp) + id; // Diagonal index of tangent 4-tensor
                    dFdgradY[idjk] = _rho * Di;
                }
            }
        }
    }

    /**
     * Flux Type: Entirely-uncoupled Fickian fluxes
     */
    else if (_type == "Fickian") {
        real D = 0, count = 0;
        for (int i = 0; i < _nsp; i++) {
            // Compute average diffusivity from interdiffusivity matrix
            for (int j = i + 1; j < _nsp; j++) {
                D += _Dij[i][j];
                count += 1.0;
            }
            D /= count;

            // Compute fluxes and tangents
            for (int dim = 0; dim < ndm; dim++) {
                F[dim + i * ndm] = _rho * D * gradY1[dim + i * ndm];
                if (compute_tangents)
                    dFdgradY[(dim + i * ndm) * (ndm * ndf) + dim + i * ndm] = _rho * D;
            }
        }
    }

    // Save flux sum to internal table
    for (int dim = 0; dim < ndm; dim++)
        internal[fluxsum_position + dim] = sum_Fstar[dim];

    return; // Done!
}

summit::real summit::MultispeciesDiffusion::capacity(real const* internal, const int component) const {
    return 0.0; // Done!
}

void summit::MultispeciesDiffusion::Source(const real* u0,
                                           const real* u1,
                                           const real* Du0,
                                           const real* Du1,
                                           real* internal,
                                           real* dtime,
                                           real* r,
                                           real* drdu,
                                           real* drdGrad,
                                           size_t ndm,
                                           size_t ndf) const {
    std::fill(drdu, drdu + (_nsp * _nsp), 0.);
    for (int i = 0; i < _nsp; i++) {
        r[i] = - _rho * (u1[i] - u0[i]) / *(dtime);
        for (int j = 0; j < _nsp; j++)
            if (i == j)
                drdu[i + _nsp * j] = - _rho / *(dtime);
    }

    return; // Done!
}

void summit::MultispeciesDiffusion::ConvectiveFlux(const real* u0,
                                                   const real* u1,
                                                   real* internal,
                                                   real* dtime,
                                                   real* F,
                                                   real* dF,
                                                   size_t ndf,
                                                   size_t ndm) const {
    return; // No advection
}

int summit::MultispeciesDiffusion::number_unknowns() const {
    return _nsp; // Done!
}

real summit::MultispeciesDiffusion::bulkModulus(real const* internal) const {
    real maxDij = _Dij[0][0];
    // Get largest diffusion coefficient
    for (const auto& row : _Dij)
        for (const auto& num : row)
            if (num > maxDij) maxDij = num;

    return maxDij;
}

real summit::MultispeciesDiffusion::Mbar(real const* Y) const {
    // Compute mean molar mass of gas from mass fractions and molar masses
    real massFractions[_nsp];
    std::copy(Y, Y + _nsp, massFractions);

    real mft = std::accumulate(massFractions, massFractions + _nsp, 0.);
    for (int k = 0; k < _nsp; k++)
        massFractions[k] /= mft * _Mi[k]; // g/mol
    real Mbar = 1. / std::accumulate(massFractions, massFractions + _nsp, 0.);

    return Mbar; // Done!
}
// end of file
