#ifndef SUMMIT_NONLINEAR_HEAT_CONDUCTION_TABLE_SOURCE_H
#define SUMMIT_NONLINEAR_HEAT_CONDUCTION_TABLE_SOURCE_H

#include "../reaction_diffusion_material.h"
#include "../../mathlib/spline.h"
#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

// Number of internal variables:
// the value is used to size the memory allocation for the material NonlinearHeatConductionTable
                                         // 0 homogeneous reactions
#define NONLINEAR_HEAT_CONDUCTION_TABLE_NUMBER_INTERNAL_VARIABLES 9

namespace summit {

/**
 *
 */
class NonlinearHeatConductionTable : public ReactionDiffusionMaterial {
  public:
    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    NonlinearHeatConductionTable(const std::string& name);

    /**
     * Destructor
     */
    virtual ~NonlinearHeatConductionTable();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    NonlinearHeatConductionTable(const NonlinearHeatConductionTable&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    NonlinearHeatConductionTable& operator=(const NonlinearHeatConductionTable&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    #ifdef WITH_YAML_CPP
    /**
     * Method to load properties from a YAML node
     * @param[in] yaml a YAML node containing properties for this material
     */
    virtual void Load(const YAML::Node& yamlNode) override;
    #endif

    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * NonlinearHeatConductionTable constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* primal,
                              const real* u1,
                              const real* Dprimal,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* primal0, const real* primal, const real* Dprimal0, const real* Dprimal, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const;

    /**
     * set the source term
     */
    void ConvectiveFlux(const real* primal0, const real* primal, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;

    real CalculateAlpha(const real primal) const;
  
      /**
     * Compute the bulk modulus
     */
    real bulkModulus(real const* internal) const;//this should be made virtual and reimplemented in future
    // also change to conductivity or something more sensible 

    /**
     * Compute the bulk modulus
     */
    void laxFriedrichStabilization(real const* primal0, real const* primal, real* C, real* dC) const;//this should be made virtual and reimplemented in future

  protected: 

    real _rho;

    real _Tref;

    summit::spline _KappaInterpolant;
    summit::spline _CpInterpolant;

    // calculated in constitutive and source
    int _INT_TEMP = 0;
    int _INT_KAPPA = 1;
    int _INT_CP = 2;

    // calculate in constitutive
    int _INT_DIFFUSIVE_THERMAL_FLUX = 3;
    int _INT_COORDINATE = 6;


  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
