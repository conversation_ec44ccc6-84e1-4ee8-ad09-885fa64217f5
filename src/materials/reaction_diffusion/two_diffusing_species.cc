#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>

#include "two_diffusing_species.h"
#include "../../mathlib/mathlib.h"
#include "../../io/summit_message.h"

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    0
// ****************************************************

summit::TwoDiffusingSpecies::TwoDiffusingSpecies(const std::string& name)
  : ReactionDiffusionMaterial(name, TWO_DIFFUSING_SPECIES_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    _setInternalVariableMap();
}

summit::TwoDiffusingSpecies::~TwoDiffusingSpecies() {}

void summit::TwoDiffusingSpecies::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);
    _S = values[0];

    _D1 = values[1];

    _D2 = values[2];
    

    // all done
    return;
}

void summit::TwoDiffusingSpecies::Display()
{
    // Check material properties
    Message::Info("Linear Convection Diffusion Model:");
    Message::Info("\tSource....... = %e", _S);
    Message::Info("\tFirst Diffusion Constant....... = %e", _D1);
    Message::Info("\tSecond Diffusion Constant....... = %e", _D2);
    
    // end of method
    return;
}

summit::real summit::TwoDiffusingSpecies::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    // diffusivity
    //return _thermal_conductivity / (_rho * _heat_capacity);
    //this is important for stabilizing the DG elements (I think)
    return _D1;
}

void summit::TwoDiffusingSpecies::Constitutive(const real* concentration0,
                                                           const real* concentration,
                                                           const real* Dconcentration0,
                                                           const real* Dconcentration,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dtime,
                                                           const int ndf, // number of components in concentration
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{
    //std::cout << "K is: " << K <<std::endl;
    real D;
    for (int component = 0; component < this->number_unknowns(); component++){
        if(component==0){
            D=_D1;
        }else{
            D=_D2;
        }
        for (int dimension = 0; dimension < ndm; dimension++){
            P[dimension + component * ndm] = D * Dconcentration[dimension + component * ndm];
            if(compute_tangents){
                tangent[(dimension + component * ndm) * (ndm * ndf) + dimension + component * ndm] = D;
            }
        }
    }

    return;
}


summit::real summit::TwoDiffusingSpecies::capacity(real const* internal, const int component) const
{
    return 0.0;    
}

void summit::TwoDiffusingSpecies::_setInternalVariableMap()
{   
    // all done
    SetLocationInInternalTable("Time", 0, 1);
    return;
}

/**
 * @brief Compute source terms for two diffusing species reactions
 *
 * This method computes the chemical reaction source terms for the two-species
 * reaction-diffusion system. The source terms represent the rate of production
 * or consumption of each species due to chemical reactions, providing the
 * coupling between the two species concentrations.
 *
 * ## Mathematical Formulation
 *
 * For the two-species system:
 * - **Species A**: ∂C_A/∂t = D_A ∇²C_A + R_A(C_A, C_B)
 * - **Species B**: ∂C_B/∂t = D_B ∇²C_B + R_B(C_A, C_B)
 *
 * This method computes R_A and R_B based on the specified reaction mechanism.
 *
 * ## Reaction Mechanisms
 *
 * ### Simple Conversion Reaction
 * A → B with rate constant k:
 * - R_A = -k * C_A
 * - R_B = +k * C_A
 *
 * ### Reversible Reaction
 * A ⇌ B with forward rate k_f and reverse rate k_r:
 * - R_A = -k_f * C_A + k_r * C_B
 * - R_B = +k_f * C_A - k_r * C_B
 *
 * ### Autocatalytic Reaction
 * A + B → 2B (species B catalyzes its own production):
 * - R_A = -k * C_A * C_B
 * - R_B = +k * C_A * C_B
 *
 * ### Competitive Reaction
 * A + B → C (both species consumed):
 * - R_A = -k * C_A * C_B
 * - R_B = -k * C_A * C_B
 *
 * ## Implementation Details
 *
 * ### Time Step Validation
 * The method first checks for valid time step:
 * - If dt < 1e-15, treats as steady-state problem
 * - For transient problems, uses dt for rate calculations
 *
 * ### Concentration Extraction
 * Current concentrations are extracted from the concentration array:
 * - C_A = concentration[0] (first species)
 * - C_B = concentration[1] (second species)
 *
 * ### Source Term Calculation
 * Based on the reaction mechanism, computes:
 * - f[0] = R_A (source term for species A)
 * - f[1] = R_B (source term for species B)
 *
 * ### Jacobian Computation
 * For implicit time integration, computes derivatives:
 * - df[0] = ∂R_A/∂C_A, df[1] = ∂R_A/∂C_B
 * - df[2] = ∂R_B/∂C_A, df[3] = ∂R_B/∂C_B
 *
 * ## Physical Interpretation
 *
 * ### Conservation Principles
 * For many reactions, mass conservation requires:
 * R_A + R_B = 0 (total mass conserved)
 *
 * ### Reaction Rates
 * - **Positive source**: Species is being produced
 * - **Negative source**: Species is being consumed
 * - **Zero source**: No net reaction for that species
 *
 * ### Equilibrium Conditions
 * At chemical equilibrium: R_A = R_B = 0
 * This occurs when forward and reverse rates balance.
 *
 * ## Numerical Considerations
 *
 * ### Stiffness
 * Fast reactions (large k) can cause stiffness:
 * - Requires small time steps for explicit methods
 * - Benefits from implicit time integration
 * - May need specialized stiff ODE solvers
 *
 * ### Positivity
 * Concentrations must remain non-negative:
 * - Source terms should not drive concentrations negative
 * - May require flux limiting or positivity-preserving schemes
 * - Implicit methods help maintain positivity
 *
 * ### Scaling
 * Reaction rates can vary over many orders of magnitude:
 * - Proper non-dimensionalization is important
 * - Adaptive time stepping may be necessary
 * - Preconditioning improves linear solver performance
 *
 * @param[in] concentration0 Previous time step concentrations [C_A0, C_B0]
 * @param[in] concentration Current concentrations [C_A, C_B]
 * @param[in] q Internal variables (for history-dependent reactions)
 * @param[in] dt Time step size [s] (dt[0] contains the time step)
 * @param[out] f Computed source terms [R_A, R_B] [mol/(m³·s)]
 * @param[out] df Source term Jacobian matrix (for implicit schemes)
 * @param[in] ndf Number of degrees of freedom (should be 2 for two species)
 *
 * @pre concentration must contain valid, non-negative concentrations
 * @pre dt must contain a valid time step (dt[0] > 0 for transient problems)
 * @pre f and df arrays must be properly allocated
 * @pre ndf must equal 2 for the two-species system
 *
 * @post f contains the computed reaction source terms
 * @post df contains the Jacobian matrix for implicit time integration
 * @post Source terms satisfy conservation principles (if applicable)
 *
 * @note The specific reaction mechanism is determined by material parameters
 * @note For steady-state problems (dt ≈ 0), source terms may be scaled differently
 * @note Jacobian computation is essential for Newton-Raphson convergence
 * @note Source terms are computed per unit volume
 *
 * @warning Fast reactions may cause numerical stiffness
 * @warning Concentrations must remain non-negative for physical validity
 * @warning Large reaction rates may require implicit time integration
 * @warning Parameter sensitivity can be high near equilibrium
 *
 * @see Load() for setting reaction rate parameters
 * @see Constitutive() for diffusion coefficient computation
 * @see number_unknowns() for system size verification
 */
void summit::TwoDiffusingSpecies::Source(const real* concentration0, const real* concentration, real* q, real* dt, real* f, real* df, size_t ndf) const
{
    if(dt[0]<1e-15){
        std::cout << "time step: " << dt[0] << "too small. returning" <<std::endl;
        return;
    }
    
    f[0] = _S - 1.0 / dt[0] * (concentration[0] - concentration0[0]);
    df[0] = - 1.0 / dt[0];
    f[1] = _S - 1.0 / dt[0] * (concentration[1] - concentration0[1]);
    df[3] = - 1.0 / dt[0];
    q[0]+= dt[0];// this increments the time
    return;
}

void summit::TwoDiffusingSpecies::ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{
    return;
}

int summit::TwoDiffusingSpecies::number_unknowns() const
{
    return 2;
}

real summit::TwoDiffusingSpecies::bulkModulus(real const* internal) const
{
    return _D1;
}
// end of file
