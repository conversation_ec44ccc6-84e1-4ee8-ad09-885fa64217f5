#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>
#include <pyre/journal.h>

#include "SquaredPressure.h"
#include "../../mathlib/mathlib.h"
#include "../../io/summit_message.h"

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    0
// ****************************************************

summit::SquaredPressure::SquaredPressure(const std::string& name)
  : ReactionDiffusionMaterial(name, SQUARED_PRESSURE_THERMAL_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    _setInternalVariableMap();
}

summit::SquaredPressure::~SquaredPressure() {}

void summit::SquaredPressure::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);
    /* properties in input file
     *
     *  0    1    2     3
     *  rho, E,   nu,   mode
     *
     *  mode 0: plane stress
     *       1: plane strain
     *       2: General 3D case
     *
     */
    
    // expect 9 parameters
    // if (values.size() != 10) {
    //     pyre::journal::error_t error("summit.materials.ArmeroSimo");
    //     // throw message and die
    //     error << "you are using artificial viscosity and I expect you to give me 9"
    //                 "input parameters in the material description file, received "
    //             << values.size() << pyre::journal::endl(__HERE__);
    // }

    _conductivity=  values[0];
    _permeability=  values[1];
    _heatCapacity=  values[2];
    _gasConstant=  values[3];
    _viscosity=  values[4];
    _molarMass=  values[5];
    _porosity=  values[6];
    _thetaRef=  values[8];
    _variational = (int)values[11];
    if(_variational){
        _phiRef=  this->_phi_from_P(values[7], _thetaRef); // values[7] is reference pressure
    }else{
        _phiRef = values[7];// now phiRef is the reference pressure
    }
    _typicalLengthScale = values[9];
    _modeOperation = (int)values[10];
    
    if(_modeOperation ==1){
        _rateConstant = values[12];
        _activationEnergy = values[13];
        _rateExponent = values[14];
        _reactionEnthalpy = values[15];
        _reactionMass = values[16];
    }
    _Coefficient = std::sqrt(_viscosity / (2 * _porosity*_gasConstant*_permeability));

    return;
}

void summit::SquaredPressure::Display()
{
    // display
    Message::Info("Kirchhoff-Transformed for Non-Isothermal Porous Medium Flow:");
    Message::Info("Heat Transfer:");
    Message::Info("\tE Thermal Conductivity....... = %e", _conductivity);
    Message::Info("\tE Heat Capacity....... = %e", _heatCapacity);
    Message::Info("Gas Transfer:");
    Message::Info("\tE Gas Permeability....... = %e", _permeability);
    Message::Info("\tE Gas Constant....... = %e", _gasConstant);
    Message::Info("\tE Gas Viscosity....... = %e", _viscosity);
    Message::Info("\tE Gas Molar Mass....... = %e", _molarMass);
    Message::Info("\tE Solid Porosity....... = %e", _porosity);
    // could be commented out because not physically meaningful to a user
    Message::Info("Fields derived from the initial condition:");
    Message::Info("\tReference Phi....... = %e", _phiRef);
    Message::Info("\tReference Temperature....... = %e", _thetaRef);
    Message::Info("\tLengthScale....... = %e", _typicalLengthScale);
    Message::Info("\tCoefficient....... = %e", _Coefficient);
    if(_modeOperation == 0){
        Message::Info("Mode Of Operation is Pure transport");
    }
    if(_modeOperation == 1){
        Message::Info("Mode Of Operation includes Pyrolysis");
        Message::Info("\tRate Constant....... = %e", _rateConstant);
        Message::Info("\tActivation Energy....... = %e", _activationEnergy);
        Message::Info("\tRate Exponent....... = %e", _rateExponent);
        Message::Info("\tReaction Enthalpy....... = %e", _reactionEnthalpy);
        Message::Info("\tReaction Mass....... = %e", _reactionMass);
    }
    if(_variational == 1){
        Message::Info("Pressure Squared Formulation");
    }


    return;
}

summit::real summit::SquaredPressure::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    // these numbers are estimates of sup(kappa)/inf(Cp)
    // where the inf and sup are taken over the temperature range of the alumina parameters
    // please do not use this model in explicit mode anyways as it is designed entirely
    // to have nice properties for implicit integrators!
    return 115.0636 / (2.5e6);
}

summit::real summit::SquaredPressure::_phi_from_P(const real P, const real T0) const
{
    return P * P * (_porosity*_molarMass*_permeability) / (2 * _gasConstant * T0 * _viscosity);
}

summit::real summit::SquaredPressure::_P_from_phi(const real phi0, const real T0) const
{
    real arg = 2 * phi0* _gasConstant * T0 * _viscosity / (_porosity*_molarMass*_permeability);
    return std::sqrt(arg);
}

void summit::SquaredPressure::Constitutive(const real* concentration0,
                                                           const real* concentration,
                                                           const real* Dconcentration0,
                                                           const real* Dconcentration,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dtime,
                                                           const int ndf, // number of components in concentration
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{
    if(_variational){
        // here we do the temperature term
        int component = 0;
        for (int dimension = 0; dimension < ndm; dimension++){
            P[dimension + component * ndm] = _conductivity * Dconcentration[dimension + component * ndm];
            if(compute_tangents){
                tangent[(dimension + component * ndm) * (ndm * ndf) + dimension + component * ndm] = _conductivity;
            }
        }
        

        // here we do the P squared term
        component = 1;
        for (int dimension = 0; dimension < ndm; dimension++){
            P[dimension + component * ndm] = Dconcentration[dimension + component * ndm];
            if(compute_tangents){
                tangent[(dimension + component * ndm) * (ndm * ndf) + dimension + component * ndm] = 1.0;
            }
        }

        // advective term
        real Phi = concentration0[1]+_phiRef;
        real Theta = concentration0[0]+_thetaRef;
        for (int dimension = 0; dimension < ndm; dimension++){
            //temperature gradient advects the gases
            q[1+dimension] = -Phi / Theta * Dconcentration0[dimension + 0 * ndm]; // this is the flux we actually want
        }
    }else{
        // here we do the linear conductivity term, term 2 of Eq 3.74
        int component = 0;
        for (int dimension = 0; dimension < ndm; dimension++){
            P[dimension + component * ndm] = _conductivity * Dconcentration[dimension + component * ndm];
            if(compute_tangents){
                tangent[(dimension + component * ndm) * (ndm * ndf) + dimension + component * ndm] = _conductivity;
            }
        }
        
        

        // here we do the linear conductivity term, term 2 of Eq 3.74
        real factor = _porosity * _molarMass * _permeability / (_gasConstant * _viscosity); // this factor are the idealized constants in the first term on the right of 3.76
        real temperature = concentration[0]+_thetaRef;
        real pressure = concentration[1]+_phiRef;
        component = 1;
        for (int dimension = 0; dimension < ndm; dimension++){
            P[dimension + component * ndm] = factor *pressure / temperature * Dconcentration[dimension + component * ndm]; // this is the flux with all the idealized constants held fixed
            if(compute_tangents){
                tangent[(dimension + component * ndm) * (ndm * ndf) + dimension + component * ndm] = factor *pressure / temperature; // this has to be the derivative of the flux with the pressure gradient
                dPdu[(dimension + ndm) * ndf + 0] = -factor *pressure / (temperature*temperature) * Dconcentration[dimension + component * ndm]; // this has to be the derivative with respect to the temperature
                dPdu[(dimension + ndm) * ndf + 1] = factor / temperature * Dconcentration[dimension + component * ndm]; // this has to be the derivative with respect to the pressure
            }
        }

        // advective term
        // real Phi = concentration0[1]+_phiRef;
        // real Theta = concentration0[0]+_thetaRef;
        // for (int dimension = 0; dimension < ndm; dimension++){
        //     //temperature gradient advects the gases
        //     q[1+dimension] = -Phi / Theta * Dconcentration0[dimension + 0 * ndm]; // this is the flux we actually want
        // }
    }

    
    return;
}


summit::real summit::SquaredPressure::capacity(real const* internal, const int component) const
{
    return 0.0;//_heatCapacity * _rho;    
}

void summit::SquaredPressure::_setInternalVariableMap()
{   
    // all done
    SetLocationInInternalTable("Pressure", 0, 1);
    SetLocationInInternalTable("Advective Flux", 1, 3);// this only matters in variational mode
    SetLocationInInternalTable("Decomposition Variable", 4, 1);
    SetLocationInInternalTable("Decomposition Rate", 5, 1);
    SetLocationInInternalTable("CFL", 6, 1);// this parameter is not important at all, please do not look at it
    SetLocationInInternalTable("Theta Rate", 7, 1);
    return;
}

void summit::SquaredPressure::Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const
{   

    if(dt[0]<1e-15){
        std::cout << "time step: " << dt[0] << "too small. returning" <<std::endl;
        return;
    }
    // the first term of eq 3.74, here heatCapacity is actually density times heat capacity
    f[0] = _heatCapacity * (concentration[0]-concentration0[0])/dt[0];
    df[0] = _heatCapacity/dt[0];

    if(_variational){
        // get pressure for storing
        real Phi = concentration[1]+_phiRef;
        real Theta = concentration[0]+_thetaRef;

        q[0] = 0.0;
        if(Phi>0){
            if(Theta>0){
                q[0] = this->_P_from_phi(Phi,Theta);
            }
        }

        real CFL = 0.0;
        if(Phi>0){
            if(Theta>0){
                CFL = std::sqrt( dt[0] * std::sqrt(Phi*Theta) / (_Coefficient *_typicalLengthScale * _typicalLengthScale ) );
            }
        }

        q[6] = CFL;
        q[7] = (concentration[0] - concentration0[0])/dt[0];
        real alpha = 1; // an algorithmic parameter

        // the following is the implicit code
        if(alpha>0){
            real C1 = _Coefficient / std::sqrt(Phi*Theta);
            real dC1dPhi = -0.5*C1 / Phi;
            real dC1dTheta =  -0.5*C1 / Theta;
            
            real C2 = C1 * Phi/Theta;
            //real dC2dPhi = dC1dPhi * Phi/Theta + C1 / Theta; // this term is not actually needed at all
            real dC2dTheta = dC1dTheta * Phi/Theta - C1 * Phi / (Theta*Theta);
            
            // here we put the transients for the pressure stuff
            f[1] = alpha*( C1 * (concentration[1]-concentration0[1])/dt[0]  );// phi dot term
            df[3] = alpha*( C1 /dt[0] + (concentration[1]-concentration0[1])/dt[0] * dC1dPhi  );// dC1 dPhi

            f[1] += alpha*( C2 * (concentration[0]-concentration0[0])/dt[0] );// theta dot term
            df[0 * 2 + 1] = alpha*( C2 /dt[0] + (concentration[0]-concentration0[0])/dt[0] * dC2dTheta);// dC2 dTheta
        }
        // the following is the explicit code

        real oldPhi = concentration0[1] + _phiRef;
        real oldTheta = concentration0[0] + _thetaRef;
        real oldThetaRate = q[7];
        real C1_Old = _Coefficient / std::sqrt(oldPhi*oldTheta);
        real C2_Old = C1_Old * oldPhi / oldTheta;
        f[1] += (1-alpha)*( C1_Old * (concentration[1]-concentration0[1])/dt[0]  );// phi dot term
        df[3] += (1-alpha)*( C1_Old /dt[0]  );// dC1 dPhi
        f[1] += (1-alpha)*( C2_Old * oldThetaRate );// theta dot term
    }else{
        q[7] = (concentration[0] - concentration0[0])/dt[0];
        q[0] = concentration[1] + _phiRef;// add reference pressure
        f[1] = -1.0 * (concentration[1]-concentration0[1])/dt[0]; // this is the 4th term on the left of Eq. 3.76. here we idealize the factor phi M / (R * theta) as one. This is incorrect, but its a first pass proof of concept of what is going on.
        df[3] = -1.0/dt[0];
    }

    // this line is key for making sure the transient terms have the correct sign
    // conventionally they appear on the other side of the equation from sources,
    // so we require a minus sign to bake them into the source function
    f[0] *= -1;
    df[0] *= -1;

    if(_modeOperation){
        real Theta = concentration[0]+_thetaRef;
        real dRhodt = _rateConstant * std::exp(-_activationEnergy / Theta) * std::pow(1.0-q[4], _rateExponent);
        
        real dRhodt_dTheta = dRhodt * _activationEnergy / (Theta * Theta)  ;

        q[4] = q[4] + dRhodt * dt[0];// integrate damage variables
        q[5] = dRhodt;

        f[0] +=  - dRhodt * _reactionEnthalpy;
        df[0] +=  - dRhodt_dTheta * _reactionEnthalpy;

        f[1] += dRhodt * _reactionMass;
        df[0 * 2 + 1] = dRhodt_dTheta * _reactionMass;

        // restrict to admissible damage
        if(q[4] > 1){
            q[4] = 1;
        }
    }

    return ;
}

void summit::SquaredPressure::ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{   
    if(_variational){
        // we use the minus sign here
        for (int dimension = 0; dimension < ndm; dimension++){
            // remember to offset by 1*ndm so that the mass flux goes in the mass transport problem, not the heat transfer
            F[1*ndm + dimension] = -q[1+dimension];// advective flux just gets read from the table
        }
    }
    return;
}

int summit::SquaredPressure::number_unknowns() const
{
    return 2;
}

real summit::SquaredPressure::bulkModulus(real const* internal) const
{
    return 1.0;
}



// end of file
