/**
 * @file nonlinear_conduction.h
 * <AUTHOR> pickard (<EMAIL>)
 * @brief A material model for nonlinear heat conduction
 * 
 * This file implements a material model for nonlinear heat conduction,
 * where thermal properties (conductivity, specific heat) depend on
 * temperature or other state variables.
 */

#ifndef SUMMIT_NONLINEAR_CONDUCTION_H
#define SUMMIT_NONLINEAR_CONDUCTION_H

#include "../reaction_diffusion_material.h"
#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

// Number of internal variables:
// the value is used to size the memory allocation for the material NonlinearConduction
                                         // 0 homogeneous reactions
#define NONLINEAR_CONDUCTION_NUMBER_INTERNAL_VARIABLES 6

namespace summit {

/**
 * @brief Implementation of a nonlinear heat conduction model
 * 
 * @details This class implements a heat conduction model with nonlinear thermal properties,
 * where conductivity and/or specific heat capacity depend on temperature or other state variables.
 * The model solves the nonlinear heat equation:
 * 
 * \f[ \rho(T) c_p(T) \frac{\partial T}{\partial t} = \nabla \cdot (k(T) \nabla T) + Q \f]
 * 
 * where \f$\rho(T)\f$ is density, \f$c_p(T)\f$ is specific heat capacity,
 * \f$k(T)\f$ is thermal conductivity, and \f$Q\f$ is a heat source term.
 * 
 * The nonlinear dependence of properties on temperature can capture phenomena such as:
 * - Phase transitions and latent heat effects
 * - Temperature-dependent material properties in extreme environments
 * - Radiation effects at high temperatures (approximated through effective conductivity)
 * - Microstructural changes affecting thermal transport
 * 
 * This implementation supports various functional forms for the temperature dependence,
 * including polynomial, exponential, and piecewise functions.
 */
class NonlinearConduction : public ReactionDiffusionMaterial {
  public:

    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    NonlinearConduction(const std::string& name);

    /**
     * Destructor
     */
    virtual ~NonlinearConduction();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    NonlinearConduction(const NonlinearConduction&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    NonlinearConduction& operator=(const NonlinearConduction&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    #ifdef WITH_YAML_CPP
    /**
     * Method to load material properties from a YAML node
     * @param[in] node the YAML node for this specific material
     */
    virtual void Load(const YAML::Node &node);
    #endif
    
    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * NonlinearConduction constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* concentration,
                              const real* u1,
                              const real* Dconcentration,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* concentration0, const real* concentration, real* q, real* dt, real* f, real* df, size_t ndf) const;


    /**
     * set the source term
     */
    void ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;

    /**
     * Compute the bulk modulus
     */
    real bulkModulus(real const* internal) const;//this should be made virtual and reimplemented in future
    // also change to conductivity or something more sensible 
  
  protected: 

    /**
     * final mass fraction
     */
    real _rho;

    /**
     * solid Cv
     */
    real _Cv;

    /**
     * first term
     */
    real _A;

    /**
     * second term
     */
    real _B;

    /**
     * third term
     */
    real _C;

    /**
     * fourth term
     */
    real _D;

    /**
     * coefficient of thermal expansion
     */
    real _cte;

    /**
     * bulk modulus
     */
    real _bulk;

    /**
     * reference temperature
     */
    real _Tref;

  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
