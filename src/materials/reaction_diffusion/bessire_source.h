#ifndef SUMMIT_BESSIRE_SOURCE_H
#define SUMMIT_BESSIRE_SOURCE_H

#include "../reaction_diffusion_material.h"

// Number of internal variables:
// the value is used to size the memory allocation for the material BessireSource
                                         // 0 homogeneous reactions
#define BESSIRE_NUMBER_INTERNAL_VARIABLES 1

namespace summit {

/**
 *
 */
class BessireSource : public ReactionDiffusionMaterial {
  public:
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    BessireSource(const std::string& name);

    /**
     * Destructor
     */
    virtual ~BessireSource();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    BessireSource(const BessireSource&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    BessireSource& operator=(const BessireSource&);

  public:

    virtual int number_unknowns() const;
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * BessireSource constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* concentration,
                              const real* u1,
                              const real* Dconcentration,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const;

    /**
     * set the source term
     */
    void ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;
  
  protected: 

    /**
     * solid thermal conductivity
     */
    real _heatCapacity;

    /**
     * solid thermal conductivity
     */
    real _thermalConductivity;

    /**
     * rate constant
     */
    real _rateConstant;

    /**
     * activation energy
     */
    real _activationTemperature;

    /**
     * final mass fraction
     */
    real _finalMassFraction;

    /**
     * final mass fraction
     */
    real _rho;

    /**
     * gas heat capacity
     */
    real _gasHeatCapacity;

    /**
     * gas diffusivity
     */
    real _gasDiffusivity;

    /**
     * equation order
     */
    real _equationOrder;

    /**
     * huge list of rates for all species at all temperatures
     */
    std::vector<real> _molarRate;

    /**
     * list of masses for relevant species
     */
    std::vector<real> _molarMass;
    
    /**
     * list of temperatures from experiment
     */
    std::vector<real> _experimentalTemperature;


    /**
     * number of intervals in experimental rate data
     */
    int timeIntervals;

    /**
     * number of species in experimental rate data
     */
    int species;
    
     


  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
