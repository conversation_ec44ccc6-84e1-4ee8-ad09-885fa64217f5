#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>

#include "bessire_source.h"
#include "../../mathlib/mathlib.h"
#include "../../io/summit_message.h"

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    0
// ****************************************************

summit::BessireSource::BessireSource(const std::string& name)
  : ReactionDiffusionMaterial(name, BESSIRE_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    _setInternalVariableMap();
}

summit::BessireSource::~BessireSource() {}

void summit::BessireSource::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);

    _rho = values[0];

    _heatCapacity = values[1];

    _thermalConductivity = values[2];

    _rateConstant = values[3];

    _activationTemperature = values[4];

    _finalMassFraction = values[5];

    _equationOrder = values[6];

    _gasHeatCapacity = values[7];

    _gasDiffusivity = values[8];
    
    timeIntervals = 14;
    
    species = 14;
    
    _molarRate.resize(timeIntervals * species,0.0);

    _molarMass.resize(species,0.0);

    _experimentalTemperature.resize(timeIntervals,0.0);

    _molarMass[0] = 2.016;
    _molarMass[1] = 16.04;
    _molarMass[2] = 28.01;
    _molarMass[3] = 44.01;
    _molarMass[4] = 94.11;
    _molarMass[5] = 18.01528;
    _molarMass[6] = 60.096;
    _molarMass[7] = 60.1;
    _molarMass[8] = 106.16;
    _molarMass[9] = 108.14;
    _molarMass[10] = 122.1644;
    _molarMass[11] = 136.19;
    _molarMass[12] = 78.11;
    _molarMass[13] = 92.14;
    
    _experimentalTemperature[0] = 147.5;
    _experimentalTemperature[1] = 206.1;
    _experimentalTemperature[2] = 285.4;
    _experimentalTemperature[3] = 373.3;
    _experimentalTemperature[4] = 452.6;
    _experimentalTemperature[5] = 511.5;
    _experimentalTemperature[6] = 563.8;
    _experimentalTemperature[7] = 620.8;
    _experimentalTemperature[8] = 687.6;
    _experimentalTemperature[9] = 765.4;
    _experimentalTemperature[10] = 844.3;
    _experimentalTemperature[11] = 923.7;
    _experimentalTemperature[12] = 1002.1;
    _experimentalTemperature[13] = 1068.5;



    //this is ridiculous but oh well!
    int index = 0;

    //hydrogen
    _molarRate[index * timeIntervals + 0] = 0.0;
    _molarRate[index * timeIntervals + 1] = 0.0;
    _molarRate[index * timeIntervals + 2] = 3.5e-8;
    _molarRate[index * timeIntervals + 3] = 7.4e-7;
    _molarRate[index * timeIntervals + 4] = 3.9e-6;
    _molarRate[index * timeIntervals + 5] = 5.5e-6;
    _molarRate[index * timeIntervals + 6] = 3.9e-6;
    _molarRate[index * timeIntervals + 7] = 2.6e-6;
    _molarRate[index * timeIntervals + 8] = 2.0e-6;
    _molarRate[index * timeIntervals + 9] = 1.6e-6;
    _molarRate[index * timeIntervals + 10] = 1.4e-6;
    _molarRate[index * timeIntervals + 11] = 1.2e-6;
    _molarRate[index * timeIntervals + 12] = 1.1e-6;
    _molarRate[index * timeIntervals + 13] = 9.7e-7;

    index +=1 ;
    //methane
    _molarRate[index * timeIntervals + 0] = 0.0;
    _molarRate[index * timeIntervals + 1] = 2.8e-8;
    _molarRate[index * timeIntervals + 2] = 6.8e-8;
    _molarRate[index * timeIntervals + 3] = 1.1e-6;
    _molarRate[index * timeIntervals + 4] = 2.2e-6;
    _molarRate[index * timeIntervals + 5] = 1.2e-6;
    _molarRate[index * timeIntervals + 6] = 4.9e-7;
    _molarRate[index * timeIntervals + 7] = 2.6e-7;
    _molarRate[index * timeIntervals + 8] = 1.6e-7;
    _molarRate[index * timeIntervals + 9] = 1.2e-7;
    _molarRate[index * timeIntervals + 10] = 9.1e-8;
    _molarRate[index * timeIntervals + 11] = 7.4e-8;
    _molarRate[index * timeIntervals + 12] = 6.6e-8;
    _molarRate[index * timeIntervals + 13] = 5.4e-8;
    
    index +=1 ;
    //carbon monoxide
    _molarRate[index * timeIntervals + 0] = 7.6e-8;
    _molarRate[index * timeIntervals + 1] = 1.5e-7;
    _molarRate[index * timeIntervals + 2] = 4.1e-7;
    _molarRate[index * timeIntervals + 3] = 1.7e-6;
    _molarRate[index * timeIntervals + 4] = 2.6e-6;
    _molarRate[index * timeIntervals + 5] = 1.7e-6;
    _molarRate[index * timeIntervals + 6] = 9.2e-7;
    _molarRate[index * timeIntervals + 7] = 6.2e-7;
    _molarRate[index * timeIntervals + 8] = 4.6e-7;
    _molarRate[index * timeIntervals + 9] = 3.8e-7;
    _molarRate[index * timeIntervals + 10] = 3.4e-7;
    _molarRate[index * timeIntervals + 11] = 3.0e-7;
    _molarRate[index * timeIntervals + 12] = 2.9e-7;
    _molarRate[index * timeIntervals + 13] = 2.6e-7; 

    index +=1 ;
    //carbon dioxide
    _molarRate[index * timeIntervals + 0] = 4.4e-8;
    _molarRate[index * timeIntervals + 1] = 4.0e-8;
    _molarRate[index * timeIntervals + 2] = 1.0e-7;
    _molarRate[index * timeIntervals + 3] = 1.5e-7;
    _molarRate[index * timeIntervals + 4] = 9.8e-8;
    _molarRate[index * timeIntervals + 5] = 4.6e-8;
    _molarRate[index * timeIntervals + 6] = 2.7e-8;
    _molarRate[index * timeIntervals + 7] = 2.0e-8;
    _molarRate[index * timeIntervals + 8] = 1.5e-8;
    _molarRate[index * timeIntervals + 9] = 1.3e-8;
    _molarRate[index * timeIntervals + 10] = 1.1e-8;
    _molarRate[index * timeIntervals + 11] = 1.0e-8;
    _molarRate[index * timeIntervals + 12] = 9.4e-9;
    _molarRate[index * timeIntervals + 13] = 8.3e-9; 

    index +=1 ;
    //phenol
    _molarRate[index * timeIntervals + 0] = 0.0;
    _molarRate[index * timeIntervals + 1] = 1.4e-8;
    _molarRate[index * timeIntervals + 2] = 1.3e-7;
    _molarRate[index * timeIntervals + 3] = 2.5e-7;
    _molarRate[index * timeIntervals + 4] = 8.6e-8;
    _molarRate[index * timeIntervals + 5] = 4.2e-8;
    _molarRate[index * timeIntervals + 6] = 2.5e-8;
    _molarRate[index * timeIntervals + 7] = 1.6e-8;
    _molarRate[index * timeIntervals + 8] = 1.1e-8;
    _molarRate[index * timeIntervals + 9] = 9.5e-9;
    _molarRate[index * timeIntervals + 10] = 7.9e-9;
    _molarRate[index * timeIntervals + 11] = 7.2e-9;
    _molarRate[index * timeIntervals + 12] = 5.7e-9;
    _molarRate[index * timeIntervals + 13] = 5.2e-9;

    index +=1 ;
    //water
    _molarRate[index * timeIntervals + 0] = 2.5e-7;
    _molarRate[index * timeIntervals + 1] = 4.6e-7;
    _molarRate[index * timeIntervals + 2] = 1.1e-6;
    _molarRate[index * timeIntervals + 3] = 2.0e-6;
    _molarRate[index * timeIntervals + 4] = 1.9e-6;
    _molarRate[index * timeIntervals + 5] = 1.2e-6;
    _molarRate[index * timeIntervals + 6] = 6.7e-7;
    _molarRate[index * timeIntervals + 7] = 4.4e-7;
    _molarRate[index * timeIntervals + 8] = 3.1e-7;
    _molarRate[index * timeIntervals + 9] = 2.5e-7;
    _molarRate[index * timeIntervals + 10] = 2.1e-7;
    _molarRate[index * timeIntervals + 11] = 1.8e-7;
    _molarRate[index * timeIntervals + 12] = 1.6e-7;
    _molarRate[index * timeIntervals + 13] = 1.5e-7;

    index +=1 ;
    //1-propanol
    _molarRate[index * timeIntervals + 0] = 1.4e-8;
    _molarRate[index * timeIntervals + 1] = 1.1e-8;
    _molarRate[index * timeIntervals + 2] = 0.0;
    _molarRate[index * timeIntervals + 3] = 0.0;
    _molarRate[index * timeIntervals + 4] = 0.0;
    _molarRate[index * timeIntervals + 5] = 0.0;
    _molarRate[index * timeIntervals + 6] = 0.0;
    _molarRate[index * timeIntervals + 7] = 0.0;
    _molarRate[index * timeIntervals + 8] = 0.0;
    _molarRate[index * timeIntervals + 9] = 0.0;
    _molarRate[index * timeIntervals + 10] = 0.0;
    _molarRate[index * timeIntervals + 11] = 0.0;
    _molarRate[index * timeIntervals + 12] = 0.0;
    _molarRate[index * timeIntervals + 13] = 0.0;

    index +=1 ;
    //2-propanol
    _molarRate[index * timeIntervals + 0] = 5.9e-9;
    _molarRate[index * timeIntervals + 1] = 3.8e-9;
    _molarRate[index * timeIntervals + 2] = 0.0;
    _molarRate[index * timeIntervals + 3] = 0.0;
    _molarRate[index * timeIntervals + 4] = 0.0;
    _molarRate[index * timeIntervals + 5] = 0.0;
    _molarRate[index * timeIntervals + 6] = 0.0;
    _molarRate[index * timeIntervals + 7] = 0.0;
    _molarRate[index * timeIntervals + 8] = 0.0;
    _molarRate[index * timeIntervals + 9] = 0.0;
    _molarRate[index * timeIntervals + 10] = 0.0;
    _molarRate[index * timeIntervals + 11] = 0.0;
    _molarRate[index * timeIntervals + 12] = 0.0;
    _molarRate[index * timeIntervals + 13] = 0.0;

    index +=1 ;
    //xylene
    _molarRate[index * timeIntervals + 0] = 0.0;
    _molarRate[index * timeIntervals + 1] = 0.0;
    _molarRate[index * timeIntervals + 2] = 2.4e-9;
    _molarRate[index * timeIntervals + 3] = 3.8e-8;
    _molarRate[index * timeIntervals + 4] = 1.6e-8;
    _molarRate[index * timeIntervals + 5] = 9.2e-9;
    _molarRate[index * timeIntervals + 6] = 5.3e-9;
    _molarRate[index * timeIntervals + 7] = 3.3e-9;
    _molarRate[index * timeIntervals + 8] = 2.5e-9;
    _molarRate[index * timeIntervals + 9] = 1.2e-9;
    _molarRate[index * timeIntervals + 10] = 1.2e-9;
    _molarRate[index * timeIntervals + 11] = 1.4e-9;
    _molarRate[index * timeIntervals + 12] = 1.4e-9;
    _molarRate[index * timeIntervals + 13] = 8.8e-10;

    index +=1 ;
    //cresol
    _molarRate[index * timeIntervals + 0] = 0.0;
    _molarRate[index * timeIntervals + 1] = 2.3e-8;
    _molarRate[index * timeIntervals + 2] = 2.1e-7;
    _molarRate[index * timeIntervals + 3] = 2.3e-7;
    _molarRate[index * timeIntervals + 4] = 7.7e-8;
    _molarRate[index * timeIntervals + 5] = 3.8e-8;
    _molarRate[index * timeIntervals + 6] = 2.3e-8;
    _molarRate[index * timeIntervals + 7] = 1.6e-8;
    _molarRate[index * timeIntervals + 8] = 1.1e-8;
    _molarRate[index * timeIntervals + 9] = 9.8e-9;
    _molarRate[index * timeIntervals + 10] = 7.7e-9;
    _molarRate[index * timeIntervals + 11] = 7.0e-9;
    _molarRate[index * timeIntervals + 12] = 6.7e-9;
    _molarRate[index * timeIntervals + 13] = 5.4e-9;

    index +=1 ;
    //dimethyl phenol
    _molarRate[index * timeIntervals + 0] = 0.0;
    _molarRate[index * timeIntervals + 1] = 2.0e-8;
    _molarRate[index * timeIntervals + 2] = 1.3e-7;
    _molarRate[index * timeIntervals + 3] = 9.6e-8;
    _molarRate[index * timeIntervals + 4] = 3.3e-8;
    _molarRate[index * timeIntervals + 5] = 2.3e-8;
    _molarRate[index * timeIntervals + 6] = 1.1e-8;
    _molarRate[index * timeIntervals + 7] = 8.0e-9;
    _molarRate[index * timeIntervals + 8] = 6.3e-9;
    _molarRate[index * timeIntervals + 9] = 4.9e-9;
    _molarRate[index * timeIntervals + 10] = 3.2e-9;
    _molarRate[index * timeIntervals + 11] = 2.7e-9;
    _molarRate[index * timeIntervals + 12] = 2.7e-9;
    _molarRate[index * timeIntervals + 13] = 2.8e-9;

    index +=1 ;
    //trimethyl phenol
    _molarRate[index * timeIntervals + 0] = 0.0;
    _molarRate[index * timeIntervals + 1] = 6.9e-9;
    _molarRate[index * timeIntervals + 2] = 2.5e-8;
    _molarRate[index * timeIntervals + 3] = 2.0e-8;
    _molarRate[index * timeIntervals + 4] = 8.9e-9;
    _molarRate[index * timeIntervals + 5] = 0.0;
    _molarRate[index * timeIntervals + 6] = 0.0;
    _molarRate[index * timeIntervals + 7] = 0.0;
    _molarRate[index * timeIntervals + 8] = 0.0;
    _molarRate[index * timeIntervals + 9] = 0.0;
    _molarRate[index * timeIntervals + 10] = 0.0;
    _molarRate[index * timeIntervals + 11] = 0.0;
    _molarRate[index * timeIntervals + 12] = 0.0;
    _molarRate[index * timeIntervals + 13] = 0.0;

    index +=1 ;
    //benzene
    _molarRate[index * timeIntervals + 0] = 0.0;
    _molarRate[index * timeIntervals + 1] = 0.0;
    _molarRate[index * timeIntervals + 2] = 0.0;
    _molarRate[index * timeIntervals + 3] = 3.3e-8;
    _molarRate[index * timeIntervals + 4] = 3.9e-8;
    _molarRate[index * timeIntervals + 5] = 1.8e-8;
    _molarRate[index * timeIntervals + 6] = 9.1e-9;
    _molarRate[index * timeIntervals + 7] = 6.5e-9;
    _molarRate[index * timeIntervals + 8] = 5.1e-9;
    _molarRate[index * timeIntervals + 9] = 4.1e-9;
    _molarRate[index * timeIntervals + 10] = 3.4e-9;
    _molarRate[index * timeIntervals + 11] = 2.8e-9;
    _molarRate[index * timeIntervals + 12] = 2.6e-9;
    _molarRate[index * timeIntervals + 13] = 2.6e-9;

    index +=1 ;
    //toluene
    _molarRate[index * timeIntervals + 0] = 0.0;
    _molarRate[index * timeIntervals + 1] = 0.0;
    _molarRate[index * timeIntervals + 2] = 8.9e-9;
    _molarRate[index * timeIntervals + 3] = 7.3e-8;
    _molarRate[index * timeIntervals + 4] = 3.8e-8;
    _molarRate[index * timeIntervals + 5] = 1.4e-8;
    _molarRate[index * timeIntervals + 6] = 8.4e-9;
    _molarRate[index * timeIntervals + 7] = 5.6e-9;
    _molarRate[index * timeIntervals + 8] = 3.6e-9;
    _molarRate[index * timeIntervals + 9] = 3.3e-9;
    _molarRate[index * timeIntervals + 10] = 2.4e-9;
    _molarRate[index * timeIntervals + 11] = 2.3e-9;
    _molarRate[index * timeIntervals + 12] = 1.6e-9;
    _molarRate[index * timeIntervals + 13] = 1.6e-9;


    // all done
    return;
}

void summit::BessireSource::Display()
{
    // Check material properties
    Message::Info("Bessire Source Term Model Material Parameters:");
    Message::Info("\tDensity....... = %e", _rho);
    Message::Info("\tHeat Capacity....... = %e", _heatCapacity);
    Message::Info("\tConductivity....... = %e", _thermalConductivity);
    Message::Info("\tRate Constant....... = %e", _rateConstant);
    Message::Info("\tActivation Temperature....... = %e", _activationTemperature);
    Message::Info("\tFinal Mass....... = %e", _finalMassFraction);    
    Message::Info("\tEquation Order....... = %e", _equationOrder);
    Message::Info("\tGas Capacity....... = %e", _gasHeatCapacity);
    Message::Info("\tGas Diffusivity....... = %e", _gasDiffusivity);  

    // end of method
    return;
}

summit::real summit::BessireSource::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    // diffusivity
    //return _thermal_conductivity / (_rho * _heat_capacity);
    return _thermalConductivity / (_rho * _heatCapacity);
}

void summit::BessireSource::Constitutive(const real* concentration,
                                                           const real* u1,
                                                           const real* Dconcentration,
                                                           const real* Du1,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dtime,
                                                           const int ndf, // number of components in concentration
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{

    std::vector<real> pressure_gradient(ndm);
    pressure_gradient[0] = 0.0;
    pressure_gradient[1] = 0.0;
    //pressure_gradient[0] = 0.0;
    
    real R = 8.314;
    real total_moles = 0.0;
    real total_mass = 0.0;
    for (int c = 1; c< ndf; c++){
        total_moles +=concentration[c];
        total_mass +=concentration[c] * _molarMass[c];
    }

    //viscosity as a function of temperature in rankine!
    real viscosity = 3.36e-7 + 4.127e-7 *(concentration[0] *9 / 5  + 491.67- 500)/ (720 - 500); // AMAR, ADAM JOSEPH. Modeling of One-Dimensional Ablation with PorousFlow Using Finite Control Volume Procedure. (Under the direction of Dr. Jack R.Edwards.)
    viscosity = viscosity * 0.3048 * 2.20462; // converting units
    real extent_of_reaction = q[0] / 38.0;// expected density loss total integration

    real permeability = 5.38e-18 + extent_of_reaction * 3.23e-17 / 0.25;
    if (extent_of_reaction > 0.25){
        permeability = 3.23e-17 + (extent_of_reaction - 0.25) * 3.23e-16 / 0.25;
    }
    if (extent_of_reaction > 0.5){
        permeability = 3.23e-16 + (extent_of_reaction - 0.5) * 2.15e-15 / 0.25;
    }
    if (extent_of_reaction > 0.75){
        permeability = 2.15e-15 + (extent_of_reaction - 0.75) * 1.08e-12 / 0.25;
    }
    //convert ft2 to m2

    permeability = 5.38e-18;
    permeability = permeability * 0.09290304;
    permeability = 3e-10;
    viscosity = 1e-5;
    real phi = 0.01 + extent_of_reaction * 0.35; // permeability is not proportional to mass loss since the solid does not have constant density
    phi = 1.0;
    real darcyConstant = viscosity / permeability;
    real Dref = 1e-4;// discussed page 44 of Duffa book
    real Mref = 50;
    for (int dimension = 0; dimension < ndm; dimension++){
        // temperature diffusion
        P[dimension + 0 * ndm] = _thermalConductivity * Dconcentration[dimension + 0 * ndm];
        // diffuse the gases
        for (int c = 1; c< ndf; c++){
            // represents diffusion with no bulk motion of gases.
            real summation = 0.0;
            real mass_fraction = concentration[c] * _molarMass[c-1] / total_mass;
            for (int k = 0; k < species;k++){
                summation += concentration[k]/(total_moles * Dref * std::sqrt(Mref / (_molarMass[k] *_molarMass[c-1]))); 
            }
            real coefficient = mass_fraction * (1 - mass_fraction) / summation;
            coefficient = 1.0e-5;
            P[dimension + c * ndm] = coefficient * Dconcentration[dimension + c * ndm];

            // gradient of the ideal gas law for mixtures
            // converting temperature to celsius
            pressure_gradient[dimension] += R * (concentration[0] + 273.15) * Dconcentration[dimension + c * ndm] / phi;
            pressure_gradient[dimension] += R * Dconcentration[dimension + 0 * ndm] * (concentration[c]) / phi;
        }

        std::cout << "pressure grad is: " << pressure_gradient[dimension] <<std::endl;
        std::cout << "darcy constant is: " << (1 / darcyConstant) <<std::endl;
        
        for (int c = 1; c< ndf; c++){
            if(std::abs(total_moles)>2e-16){
                //std::cout << "velocity is: " << 1 / darcyConstant * pressure_gradient[dimension] <<std::endl;
                P[dimension + c * ndm] += -(1 / darcyConstant) * pressure_gradient[dimension] * concentration[c];// bulk diffusion due to darcian flow
            }
        }
    }
    // std::cout << "velocity is: " << (1 / darcyConstant) * std::sqrt(pressure_gradient[0] * pressure_gradient[0] + pressure_gradient[1] * pressure_gradient[1]  + pressure_gradient[2] * pressure_gradient[2] ) <<std::endl;
    // end done
    return;
}


summit::real summit::BessireSource::capacity(real const* internal, const int component) const
{
    // all done
    if (component == 0){
        return _heatCapacity * _rho;
    }
    return 1.0;// adding 1 mole per unit volume of gas raises the concentration per unit volume in ref config by exactly one
    //we will account for mechanical compression elsewhere, all data with respect to ref config.
}

void summit::BessireSource::_setInternalVariableMap()
{

    SetLocationInInternalTable("Density Lost", 0, 1);// running integration of mass flux from source terms
    // all done
    return;
}

void summit::BessireSource::Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const
{
    // create the rate constants
    real K = _rateConstant * std::exp(-_activationTemperature / concentration[0]);
    real temperature = concentration[0];
    real temperatureRate = (concentration[0] - concentration0[0]) / dt[0];
    int whichTemp = 0;
    if (temperature > _experimentalTemperature[timeIntervals-1]){
        //std::cout << "no pyrolysis, too hot"<<std::endl;
        return;
    }
    if (temperature < _experimentalTemperature[0]){
        //std::cout << "no pyrolysis, too cold"<<std::endl;
        return;
    }
    while (temperature > _experimentalTemperature[whichTemp]){
        whichTemp += 1;
    }
    real upper = _experimentalTemperature[whichTemp];
    real lower = _experimentalTemperature[whichTemp-1];
    real percentLower = std::abs(upper - temperature) / (upper-lower);
    real percentUpper = 1 - percentLower;
    real correction_factor = 1.575e-6;
    for (int component = 0; component<species;component++){
        // mol per second per unit volume in reference configuration
        f[1+component] = temperatureRate * _molarRate[component * timeIntervals + whichTemp] * percentUpper + temperatureRate * _molarRate[component * timeIntervals + whichTemp - 1] * percentLower;
        f[1+component] *=1 / correction_factor;// nondimensionalize their experiment with respect to ref volume
    }

    for (int component = 0; component<species;component++){
        // mass loss in terms of kg/s per unit volume in the reference configuration
        q[0] += f[component+1] * _molarMass[component] * dt[0] * 1e-3;// integrate the mass lost
        
    }
    
    // end done
    return;
}

void summit::BessireSource::ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{
    return;
}

int summit::BessireSource::number_unknowns() const
{
    return 14;
}
// end of file
