#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>

#include "TorresHerrador.h"
#include "../../mathlib/mathlib.h"
#include "../../io/summit_message.h"

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    0
// ****************************************************

summit::TorresHerrador::TorresHerrador(const std::string& name)
  : ReactionDiffusionMaterial(name, TORRES_HERRADOR_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    _setInternalVariableMap();
}

summit::TorresHerrador::~TorresHerrador() {}

void summit::TorresHerrador::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);

    _rho = values[0];
    _heatCapacity = values[1];
    _thermalConductivity = values[2];
    _Permeability = values[3];
    _mu = values[4];

    _kChar = 2.0e-11;
    _kVirgin = 1.6e-11;

    _phiChar = 0.85;
    _phiVirgin = 0.8;

    _Temperatures.resize(13,0);
    _Temperatures[0] = 2.556E+02;
    _Temperatures[1] = 2.980E+02;
    _Temperatures[2] = 4.444E+02;
    _Temperatures[3] = 5.556E+02;
    _Temperatures[4] = 6.444E+02;
    _Temperatures[5] = 8.333E+02;
    _Temperatures[6] = 1.111E+03;
    _Temperatures[7] = 1.389E+03;
    _Temperatures[8] = 1.667E+03;
    _Temperatures[9] = 1.944E+03;
    _Temperatures[10] = 2.222E+03;
    _Temperatures[11] = 2.778E+03;
    _Temperatures[12] = 3.333E+03;

    _CapacitiesVirgin.resize(13,0);
    _CapacitiesVirgin[0] = 8.792E+02;
    _CapacitiesVirgin[1] = 9.839E+02;
    _CapacitiesVirgin[2] = 1.298E+03;
    _CapacitiesVirgin[3] = 1.465E+03;
    _CapacitiesVirgin[4] = 1.570E+03;
    _CapacitiesVirgin[5] = 1.717E+03;
    _CapacitiesVirgin[6] = 1.863E+03;
    _CapacitiesVirgin[7] = 1.934E+03;
    _CapacitiesVirgin[8] = 1.980E+03;
    _CapacitiesVirgin[9] = 1.989E+03;
    _CapacitiesVirgin[10] = 2.001E+03;
    _CapacitiesVirgin[11] = 2.010E+03;
    _CapacitiesVirgin[12] = 2.010E+03;

    _ThermalConductivitiesVirgin.resize(13,0);
    _ThermalConductivitiesVirgin[0] = 3.975E-01;
    _ThermalConductivitiesVirgin[1] = 4.025E-01;
    _ThermalConductivitiesVirgin[2] = 4.162E-01;
    _ThermalConductivitiesVirgin[3] = 4.530E-01;
    _ThermalConductivitiesVirgin[4] = 4.698E-01;
    _ThermalConductivitiesVirgin[5] = 4.860E-01;
    _ThermalConductivitiesVirgin[6] = 5.234E-01;
    _ThermalConductivitiesVirgin[7] = 5.601E-01;
    _ThermalConductivitiesVirgin[8] = 6.978E-01;
    _ThermalConductivitiesVirgin[9] = 8.723E-01;
    _ThermalConductivitiesVirgin[10] = 1.109E+00;
    _ThermalConductivitiesVirgin[11] = 1.751E+00;
    _ThermalConductivitiesVirgin[12] = 2.779E+00;

    _ThermalConductivitiesChar.resize(13,0);
    _ThermalConductivitiesChar[0] = 3.975E-01;
    _ThermalConductivitiesChar[1] = 4.025E-01;
    _ThermalConductivitiesChar[2] = 4.162E-01;
    _ThermalConductivitiesChar[3] = 4.530E-01;
    _ThermalConductivitiesChar[4] = 4.698E-01;
    _ThermalConductivitiesChar[5] = 4.860E-01;
    _ThermalConductivitiesChar[6] = 5.234E-01;
    _ThermalConductivitiesChar[7] = 5.601E-01;
    _ThermalConductivitiesChar[8] = 6.050E-01;
    _ThermalConductivitiesChar[9] = 7.290E-01;
    _ThermalConductivitiesChar[10] = 9.221E-01;
    _ThermalConductivitiesChar[11] = 1.458E+00;
    _ThermalConductivitiesChar[12] = 2.318E+00;

    _CapacitiesChar.resize(13,0);
    _CapacitiesChar[0] = 7.327E+02;
    _CapacitiesChar[1] = 7.829E+02;
    _CapacitiesChar[2] = 1.093E+03;
    _CapacitiesChar[3] = 1.319E+03;
    _CapacitiesChar[4] = 1.432E+03;
    _CapacitiesChar[5] = 1.675E+03;
    _CapacitiesChar[6] = 1.842E+03;
    _CapacitiesChar[7] = 1.968E+03;
    _CapacitiesChar[8] = 2.052E+03;
    _CapacitiesChar[9] = 2.093E+03;
    _CapacitiesChar[10] = 2.110E+03;
    _CapacitiesChar[11] = 2.135E+03;
    _CapacitiesChar[12] = 2.152E+03;

    _EnthalpiesVirgin.resize(13,0);
    _EnthalpiesVirgin[0] = -8.967E+05;
    _EnthalpiesVirgin[1] = -8.57E+05;
    _EnthalpiesVirgin[2] = -6.901E+05;
    _EnthalpiesVirgin[3] = -5.365E+05;
    _EnthalpiesVirgin[4] = -4.016E+05;
    _EnthalpiesVirgin[5] = -9.124E+04;
    _EnthalpiesVirgin[6] = 4.059E+05;
    _EnthalpiesVirgin[7] = 9.334E+05;
    _EnthalpiesVirgin[8] = 1.477E+06;
    _EnthalpiesVirgin[9] = 2.028E+06;
    _EnthalpiesVirgin[10] = 2.583E+06;
    _EnthalpiesVirgin[11] = 3.697E+06;
    _EnthalpiesVirgin[12] = 4.813E+06;

    _EnthalpiesChar.resize(13,0);
    _EnthalpiesChar[0] = -3.216E+04;
    _EnthalpiesChar[1] = 0.000E+00;
    _EnthalpiesChar[2] = 1.373E+05;
    _EnthalpiesChar[3] = 2.713E+05;
    _EnthalpiesChar[4] = 3.936E+05;
    _EnthalpiesChar[5] = 6.870E+05;
    _EnthalpiesChar[6] = 1.175E+06;
    _EnthalpiesChar[7] = 1.705E+06;
    _EnthalpiesChar[8] = 2.263E+06;
    _EnthalpiesChar[9] = 2.839E+06;
    _EnthalpiesChar[10] = 3.422E+06;
    _EnthalpiesChar[11] = 4.602E+06;
    _EnthalpiesChar[12] = 5.793E+06;

    _KappaCharInterpolant.set_points(_Temperatures, _ThermalConductivitiesChar);
    _KappaVirgInterpolant.set_points(_Temperatures, _ThermalConductivitiesVirgin);

    _CpCharInterpolant.set_points(_Temperatures, _CapacitiesChar);
    _CpVirgInterpolant.set_points(_Temperatures, _CapacitiesVirgin);

    _EnthalpiesCharInterpolant.set_points(_Temperatures, _EnthalpiesChar);
    _EnthalpiesVirgInterpolant.set_points(_Temperatures, _EnthalpiesVirgin);

    InitializeGasProperties();

    _molarMass.resize(12,0.0);

    _molarMass[0] = 2.016;//hydrogen
    _molarMass[1] = 16.04;//methane 
    _molarMass[2] = 28.01;//carbone monoxide
    _molarMass[3] = 44.01;//carbon dioxide
    _molarMass[4] = 94.11;//c6h6o
    _molarMass[5] = 18.01528;//water
    _molarMass[6] = 60.096;//c3h8o
    _molarMass[7] = 60.1;//c3h8o
    _molarMass[8] = 106.16;//c8h1o
    _molarMass[9] = 108.14;//c7h8o
    _molarMass[10] = 78.11;//c6h6
    _molarMass[11] = 92.14;//c7h8

    _rates.resize(6,0);
    _rates[0] = 6.59;
    _rates[1] = 6.96;
    _rates[2] = 6.71;
    _rates[3] = 6.67;
    _rates[4] = 6.58;
    _rates[5] = 6.35;
    
    _activationEnergy.resize(6,0);
    _activationEnergy[0] = 77.6;
    _activationEnergy[1] = 61.3;
    _activationEnergy[2] = 95.1;
    _activationEnergy[3] = 103.0;
    _activationEnergy[4] = 113.9;
    _activationEnergy[5] = 175.2;

    _exponent.resize(6,0);
    _exponent[0] = 5.65;
    _exponent[1] = 9.96;
    _exponent[2] = 4.23;
    _exponent[3] = 4.38;
    _exponent[4] = 6.68;
    _exponent[5] = 8.85;

    _resinFraction.resize(6,0);
    _resinFraction[0] = 0.06;
    _resinFraction[1] = 0.009;
    _resinFraction[2] = 0.203;
    _resinFraction[3] = 0.187;
    _resinFraction[4] = 0.026;
    _resinFraction[5] = 0.059;

    _gasFraction.resize(17,0);
    _gasFraction[0] = 0.62;
    _gasFraction[1] = 0.38;
    _gasFraction[2] = 0.69;
    _gasFraction[3] = 0.21;
    _gasFraction[4] = 0.09;
    _gasFraction[5] = 0.42;
    _gasFraction[6] = 0.06;
    _gasFraction[7] = 0.15;
    _gasFraction[8] = 0.34;
    _gasFraction[9] = 0.03;
    _gasFraction[10] = 0.67;
    _gasFraction[11] = 0.27;
    _gasFraction[12] = 0.03;
    _gasFraction[13] = 0.04;
    _gasFraction[14] = 1.0;
    _gasFraction[15] = 0.19;
    _gasFraction[16] = 0.81;

    _stoichiometricCoefficients.resize(6);
    _stoichiometricCoefficients[0] = {0.62, 0.38};
    _stoichiometricCoefficients[1] = {0.69, 0.21, 0.09};
    _stoichiometricCoefficients[2] = {0.42, 0.06, 0.15, 0.34, 0.03};
    _stoichiometricCoefficients[3] = {0.67, 0.27, 0.03, 0.04};
    _stoichiometricCoefficients[4] = {1.0};
    _stoichiometricCoefficients[5] = {0.19, 0.81};
    

    // all done
    return;
}

void summit::TorresHerrador::Display()
{
    // Check material properties
    Message::Info("Bessire Source Term Model For Single Gas:");
    Message::Info("\tDensity....... = %e", _rho);
    Message::Info("\tHeat Capacity....... = %e", _heatCapacity);
    Message::Info("\tThermal Conductivity....... = %e", _thermalConductivity);
    Message::Info("\tPermeability....... = %e", _Permeability);
    Message::Info("\tViscosity....... = %e", _mu);
    // end of method
    return;
}

summit::real summit::TorresHerrador::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    
    real rhos = q[_INT_SOLID_DENS];
    real kappa = q[_INT_KAPPA];
    real Cp = q[_INT_CP];

    if (rhos == 0.0){
        rhos = 280.0;
    }

    if (kappa = 0.0){
        kappa = 0.5;
    }

    if (Cp == 0.0){
        Cp = 1400.0;
    }

    // diffusivity
    return kappa / (rhos * Cp);
}

void summit::TorresHerrador::Constitutive(const real* primal0,
                                                           const real* primal,
                                                           const real* Dprimal0,
                                                           const real* Dprimal,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dt,
                                                           const int ndf, // number of components in primal
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{

    if (primal[0] <= 0.0){
        return;
    }

    // calculateDecompositionRate(primal, q, &dt);
    calculateTau(primal, q);
    calculatePartialHeatOfCharring(primal, q);
    calculateGasEnthalpy(primal, q);
    
    GetThermalConductivity(primal, q);

    real K = q[_INT_KAPPA];
    real dK = q[_INT_DKAPPA_DT];

    int strain_dim = ndm * ndf;
    int component = 0;
    for (int dimension = 0; dimension < ndm; dimension++){
        P[dimension + component * ndm] = K * Dprimal[dimension + component * ndm];
        if(compute_tangents){
            tangent[(dimension + component * ndm) * (ndm * ndf) + dimension + component * ndm] = K;
            dPdu[(dimension + component * ndm) * ndf + component] = dK * Dprimal[dimension + component * ndm];
        }
    }

    calculateGasMassFlux(primal, Dprimal, q);

    real k = _kVirgin;
    real R = 8.3145;
    real T = primal[0];
    real p = primal[1];
    real M = _GasMolarWeightInterpolant(T)/1000.0;
    real dM_dT = _GasMolarWeightInterpolant.deriv(1, T)/1000.0;
    real mu = _GasViscosityInterpolant(T)*0.0001;
    real dmu_dT = _GasViscosityInterpolant.deriv(1, T)*0.0001;

    component = 1;
    for (int dimension = 0; dimension < ndm; dimension++){
        P[dimension + component * ndm] = p * M * k / (mu * R * T) * Dprimal[dimension + component * ndm];
        if(compute_tangents){
            tangent[(dimension + component * ndm) * (ndm * ndf) + dimension + component * ndm] = p * M * k / (mu * R * T);
            // dPdu[(dimension + component * ndm) * ndf + 0] = p * k / (mu * R) * (dM_dT / T - M / (T * T)) * Dprimal[dimension + component * ndm];
            dPdu[(dimension + component * ndm) * ndf + 0] = (k / R) * (dM_dT / (mu * T) - M / (mu * T * T) - (M * dmu_dT) / (mu * mu * T)) * Dprimal[dimension + component * ndm];
            dPdu[(dimension + component * ndm) * ndf + 1] = M * k / (mu * R * T) * Dprimal[dimension + component * ndm];
        }
    }

    // this sets the temperature in the internals for transfer to the solid!
    q[_INT_TEMP] = primal[0];
    
    return;
}

summit::real summit::TorresHerrador::capacity(real const* q, const int component) const
{   
    return 0.0;
}

void summit::TorresHerrador::_setInternalVariableMap()
{
    SetLocationInInternalTable("Temperature", _INT_TEMP, 1);
    
    SetLocationInInternalTable("Extent of Reaction Rate", _INT_EXTENT_OF_REACTION_RATE, _NUM_OF_REACTIONS);
    SetLocationInInternalTable("Extent of Reaction", _INT_EXTENT_OF_REACTION, _NUM_OF_REACTIONS);
    SetLocationInInternalTable("Extent of Reaction Tangent", _INT_EXTENT_OF_REACTION_RATE_DT, _NUM_OF_REACTIONS);
    SetLocationInInternalTable("Component Gas Production Rate", _INT_COMPONENT_GAS_PRODUCTION_RATE, _NUM_OF_GAS_PRODUCTS);
    
    SetLocationInInternalTable("Decomposition Rate", _INT_DECOMP_RATE, 1);
    SetLocationInInternalTable("Decomposition Rate Tangent", _INT_DDECOMP_RATE_DT, 1);
    
    SetLocationInInternalTable("Solid Density", _INT_SOLID_DENS, 1);
    SetLocationInInternalTable("Solid Density Tangent", _INT_DSOLID_DENS_DT, 1);

    SetLocationInInternalTable("Gas Enthalpy", _INT_GAS_ENTHALPY, 1);
    SetLocationInInternalTable("Char Enthalpy", _INT_H_CHAR, 1);
    SetLocationInInternalTable("Virgin Enthalpy", _INT_H_VIRGIN, 1);
    SetLocationInInternalTable("Partial Heat of Charring", _INT_PARTIAL_HEAT_CHAR, 1);
    SetLocationInInternalTable("Gas Enthalpy Tangent", _INT_DGAS_ENTHALPY_DT, 1);
    SetLocationInInternalTable("Partial Heat of Charring Tangent", _INT_DPARTIAL_HEAT_CHAR_DT, 1);

    SetLocationInInternalTable("Kappa Char", _INT_KAPPA_CHAR, 1);
    SetLocationInInternalTable("Kappa Virgin", _INT_KAPPA_VIRGIN, 1);
    SetLocationInInternalTable("Kappa", _INT_KAPPA, 1);
    SetLocationInInternalTable("Kappa Tangent", _INT_DKAPPA_DT, 1);

    SetLocationInInternalTable("Cp Char", _INT_CP_CHAR, 1);
    SetLocationInInternalTable("Cp Virgin", _INT_CP_VIRGIN, 1);
    SetLocationInInternalTable("Cp", _INT_CP, 1);
    SetLocationInInternalTable("Cp Tangent", _INT_DCP_DT, 1);

    SetLocationInInternalTable("Degree of Char", _INT_DEGREE_OF_CHAR, 1);
    SetLocationInInternalTable("Tau", _INT_TAU, 1);
    SetLocationInInternalTable("Tau Tangent", _INT_DTAU_DT, 1);

    SetLocationInInternalTable("Mass Flux", _INT_MDOT, 3);

    SetLocationInInternalTable("Gas Velocity", _INT_GAS_VELOCITY, 3);
    
    // all done
    return;
}

void summit::TorresHerrador::calculateTau(const real * primal, real * q) const
{
    real rhos = q[_INT_SOLID_DENS];
    if (rhos == 0.0){
        rhos = 280.0;
    }
    real rhoc = _Rho_Total_Char;
    real rhov = _Rho_Total_Virgin;
    q[_INT_TAU] = (1.0 - rhoc / rhos) / (1.0 - rhoc / rhov);
    q[_INT_DTAU_DT] = rhoc/((1-rhoc/rhov)*rhos*rhos)*q[_INT_DSOLID_DENS_DT];
    q[_INT_DEGREE_OF_CHAR] = (rhov - rhos)/(rhov - rhoc);
    return;
}

void summit::TorresHerrador::Source(const real* primal0, const real* primal, const real* Dprimal0, const real* Dprimal, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const
{   

    calculateDecompositionRate(primal, q, dt);
    calculateTau(primal, q);
    calculatePartialHeatOfCharring(primal, q);
    calculateGasEnthalpy(primal, q);
    GetThermalConductivity(primal, q);
    GetCp(primal, q);


    real hg = q[_INT_GAS_ENTHALPY];
    real h_bar = q[_INT_PARTIAL_HEAT_CHAR];

    real dhg_dT = q[_INT_DGAS_ENTHALPY_DT];
    real dh_bar_dT = q[_INT_DPARTIAL_HEAT_CHAR_DT];
    real ddrhos_dt_dT = q[_INT_DDECOMP_RATE_DT];


    real drhos_dt = q[_INT_DECOMP_RATE];

    real rhos = q[_INT_SOLID_DENS];
    real Cp = q[_INT_CP];
    real dCp_dT = q[_INT_DCP_DT];
    real dRho_dT = q[_INT_DSOLID_DENS_DT];


    f[0] = - rhos * Cp * (primal[0] - primal0[0]) / dt[0];
    f[0] += (hg - h_bar) * drhos_dt;
    df[0] = - rhos * Cp / dt[0]; 
    df[0] += - dRho_dT * Cp * (primal[0] - primal0[0]) / dt[0];
    df[0] += - rhos * dCp_dT * (primal[0] - primal0[0]) / dt[0];
    df[0] += dhg_dT * drhos_dt;
    df[0] += - dh_bar_dT * drhos_dt;
    df[0] += (hg - h_bar) * ddrhos_dt_dT;

    real R = 8.3145;
    real T = primal[0];
    real p = primal[1];
    real phi = _phiVirgin;
    real M = _GasMolarWeightInterpolant(T)/1000.0;
    real dM_dT = _GasMolarWeightInterpolant.deriv(1, T)/1000.0;
    real d2M_dT2 = _GasMolarWeightInterpolant.deriv(2, T)/1000.0;

    f[1] = 0.0;
    f[1] -= M * phi / (R * T) * (primal[1] - primal0[1]) / dt[0];
    f[1] -= phi * p / R * (dM_dT / T - M / (T * T)) * (primal[0] - primal0[0]) / dt[0];
    f[1] -= drhos_dt;

    df[2] = 0.0;
    df[2] -= (phi / R) * (dM_dT / T - M / (T * T)) * (primal[1] - primal0[1]) / dt[0];
    df[2] -= (phi * p / R ) * (dM_dT / T - M / (T * T)) / dt[0];
    df[2] -= (phi * p / R ) * (2 * M / (T * T  *T) - dM_dT / (T * T) + d2M_dT2 / T) * (primal[0] - primal0[0]) / dt[0];

    df[3] = 0.0;
    df[3] -= (phi * M) / (R * T) / dt[0];
    df[3] -= (phi / R) * (dM_dT / T - M / (T *T)) * (primal[0] - primal0[0]) / dt[0];
    return;
}

void summit::TorresHerrador::GetThermalConductivity(const real * primal, real * q) const
{

    real T = primal[0];
    real Kappa = _thermalConductivity;
    real dKappa_dT = 0.0;
    real dKappa_dTau = 0.0;
    real Tau = q[_INT_TAU];
    real dTau_dT = q[_INT_DTAU_DT];
    
    real KappaChar = _KappaCharInterpolant(T);
    real KappaVirgin = _KappaVirgInterpolant(T);

    q[_INT_KAPPA_CHAR] = KappaChar;
    q[_INT_KAPPA_VIRGIN] = KappaVirgin;

    real dKappaChar_dT = _KappaCharInterpolant.deriv(1, T);
    real dKappaVirgin_dT = _KappaVirgInterpolant.deriv(1, T);

    Kappa = KappaVirgin * Tau + KappaChar * (1.0 - Tau);
    dKappa_dT = dKappaVirgin_dT * Tau + dKappaChar_dT * (1.0 - Tau) + dTau_dT*(KappaVirgin - KappaChar);

    q[_INT_KAPPA] = Kappa;
    q[_INT_DKAPPA_DT] = dKappa_dT;

    return;
}

void summit::TorresHerrador::GetCp(const real * primal, real * q) const
{
    real T = primal[0];
    real Tau = q[_INT_TAU];
    real Cp = 0.0;
    real dCp_dT = 0.0;
    real dCp_dTau = q[_INT_DTAU_DT];

    real CpChar = _CpCharInterpolant(T);
    real CpVirgin = _CpVirgInterpolant(T);

    q[_INT_CP_CHAR] = CpChar;
    q[_INT_CP_VIRGIN] = CpVirgin;

    real dCpChar_dT = _CpCharInterpolant.deriv(1, T);
    real dCpVirgin_dT = _CpVirgInterpolant.deriv(1, T);

    Cp = CpVirgin * Tau + CpChar * (1.0 - Tau);
    dCp_dT = dCpVirgin_dT * Tau + dCpChar_dT * (1.0 - Tau) + dCp_dTau*(CpVirgin - CpChar);

    q[_INT_CP] = Cp;
    q[_INT_DCP_DT] = dCp_dT;

    return;
}

void summit::TorresHerrador::calculateGasMassFlux(const real * primal, const real * Dprimal, real * q) const
{

    real k = _kVirgin;
    real R = 8.3145;
    real T = primal[0];
    real p = primal[1];
    real M = _GasMolarWeightInterpolant(T)/1000.0;
    real mu = _GasViscosityInterpolant(T)*0.0001;
    real phi = _phiVirgin;

    real vgx = - k / mu * Dprimal[2];
    real vgy = - k / mu * Dprimal[3];

    q[_INT_GAS_VELOCITY + 0] = vgx;
    q[_INT_GAS_VELOCITY + 1] = vgy;
    
    // real vgz = - k / mu * Dprimal[5];

    real rhog = p * M / (R * T);

    q[_INT_GAS_DENSITY] = rhog;


    q[_INT_MDOT + 0] = rhog * vgx;
    q[_INT_MDOT + 1] = rhog * vgy;
    // q[_INT_MDOT + 2] = rhog * vgz;
    return;
}

void summit::TorresHerrador::calculateGasEnthalpy(const real * primal, real * q) const
{

    real T = primal[0];
    real Hg = 0.0;
    real dHg_dT = 0.0;

    Hg = _GasEnthalpyInterpolant(T);
    dHg_dT = _GasEnthalpyInterpolant.deriv(1, T);

    q[_INT_GAS_ENTHALPY] = Hg*1.0e3;
    q[_INT_DGAS_ENTHALPY_DT] = dHg_dT*1.0e3;

    return;
}


void summit::TorresHerrador::calculateDecompositionRate(const real * primal, real * q, real * dt) const
{

    real R = 8.314;
    real T = primal[0];

    real _ResinDensity = 1200.0;
    real _ResinVolumeFraction = 0.1;
    real phi = 0.8;

    q[_INT_SOLID_DENS] = _Rho_Total_Virgin;
    q[_INT_DECOMP_RATE] = 0.0;
    q[_INT_DSOLID_DENS_DT] = 0.0;
    q[_INT_DDECOMP_RATE_DT] = 0.0;
    
    int k = 0;
    for (int i = 0; i < 6; ++i){

        real extentOfReaction = q[_INT_EXTENT_OF_REACTION+i]; 

        real A = std::pow(10, _rates[i]);
        real B = std::pow((1 - extentOfReaction), _exponent[i]);
        real C = std::exp(-_activationEnergy[i] * 1000.0 / (T * R));
        
        real extentOfReactionRate = A * B * C;
        extentOfReaction += extentOfReactionRate * dt[0];

        if(extentOfReaction > 1.0){
            std::cout << "Reaction number: " << i << "  has gone over 100 percent and is at: " << extentOfReaction <<std::endl;
            std::cout << "A: " << A  <<std::endl;
            std::cout << "B: " << B  <<std::endl;
            std::cout << "C: " << C  <<std::endl;
            std::cout << "3dxdt: " << extentOfReactionRate  <<std::endl;
            std::cout << "temperature: " << T  <<std::endl;

            //set the reaction to be complete
            extentOfReactionRate -= (extentOfReaction - 1.0)/dt[0];
            extentOfReaction = 1.0;
        }


        q[_INT_EXTENT_OF_REACTION_RATE+i] = extentOfReactionRate;
        q[_INT_EXTENT_OF_REACTION+i] = extentOfReaction;

        q[_INT_SOLID_DENS] -= _resinFraction[i] * extentOfReaction * _ResinDensity * _ResinVolumeFraction;
        q[_INT_DECOMP_RATE] -= _resinFraction[i] * extentOfReactionRate * _ResinDensity * _ResinVolumeFraction;
        q[_INT_DSOLID_DENS_DT] -= _resinFraction[i] * extentOfReaction * _ResinDensity * _ResinVolumeFraction * (_activationEnergy[i] * 1000.0 / (R * T * T));
        q[_INT_DDECOMP_RATE_DT] -= _resinFraction[i] * extentOfReactionRate * _ResinDensity * _ResinVolumeFraction * (_activationEnergy[i] * 1000.0 / (R * T * T));
        
        for (int j = 0; j < _stoichiometricCoefficients[i].size(); j++){
            real Xi = _stoichiometricCoefficients[i][j];
            q[_INT_COMPONENT_GAS_PRODUCTION_RATE + k] = _resinFraction[i] * extentOfReactionRate * _ResinDensity * _ResinVolumeFraction * Xi;
            k++;
        }
    }

    return;
}

void summit::TorresHerrador::calculatePartialHeatOfCharring(const real * primal, real * q) const
{

    real T = primal[0];
    real rho_c = _Rho_Total_Char;
    real rho_v = _Rho_Total_Virgin;

    real hc = _EnthalpiesCharInterpolant(T);
    real hv = _EnthalpiesVirgInterpolant(T);

    q[_INT_H_CHAR] = hc;
    q[_INT_H_VIRGIN] = hv;

    real dhc_dT = _EnthalpiesCharInterpolant.deriv(1, T);
    real dhv_dT = _EnthalpiesVirgInterpolant.deriv(1, T);
    
    q[_INT_PARTIAL_HEAT_CHAR] = (rho_v*hv-rho_c*hc)/(rho_v - rho_c);
    q[_INT_DPARTIAL_HEAT_CHAR_DT] = (rho_v*dhv_dT - rho_c*dhc_dT)/(rho_v - rho_c);

    return;
}


void summit::TorresHerrador::ConvectiveFlux(const real* primal0, const real* primal, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{
    return;
}

int summit::TorresHerrador::number_unknowns() const{
    return 2;
}

real summit::TorresHerrador::bulkModulus(real const* q) const
{
    real kappa = q[_INT_KAPPA];
    if (kappa == 0.0){
        kappa = 3.5;
    }
    return kappa;
}

void summit::TorresHerrador::laxFriedrichStabilization(const real* primal0, const real* primal, real* C, real* dC) const
{   
    C[0] = 0.3;
    return;
}

void summit::TorresHerrador::InitializeGasProperties()
{
    _TemperaturesPyrolysisGas = {
    2.000E+02,
    2.250E+02,
    2.500E+02,
    2.750E+02,
    3.000E+02,
    3.250E+02,
    3.500E+02,
    3.750E+02,
    4.000E+02,
    4.250E+02,
    4.500E+02,
    4.750E+02,
    5.000E+02,
    5.250E+02,
    5.500E+02,
    5.750E+02,
    6.000E+02,
    6.250E+02,
    6.500E+02,
    6.750E+02,
    7.000E+02,
    7.250E+02,
    7.500E+02,
    7.750E+02,
    8.000E+02,
    8.250E+02,
    8.500E+02,
    8.750E+02,
    9.000E+02,
    9.250E+02,
    9.500E+02,
    9.750E+02,
    1.000E+03,
    1.025E+03,
    1.050E+03,
    1.075E+03,
    1.100E+03,
    1.125E+03,
    1.150E+03,
    1.175E+03,
    1.200E+03,
    1.225E+03,
    1.250E+03,
    1.275E+03,
    1.300E+03,
    1.325E+03,
    1.350E+03,
    1.375E+03,
    1.400E+03,
    1.425E+03,
    1.450E+03,
    1.475E+03,
    1.500E+03,
    1.525E+03,
    1.550E+03,
    1.575E+03,
    1.600E+03,
    1.625E+03,
    1.650E+03,
    1.675E+03,
    1.700E+03,
    1.725E+03,
    1.750E+03,
    1.775E+03,
    1.800E+03,
    1.825E+03,
    1.850E+03,
    1.875E+03,
    1.900E+03,
    1.925E+03,
    1.950E+03,
    1.975E+03,
    2.000E+03,
    2.025E+03,
    2.050E+03,
    2.075E+03,
    2.100E+03,
    2.125E+03,
    2.150E+03,
    2.175E+03,
    2.200E+03,
    2.225E+03,
    2.250E+03,
    2.275E+03,
    2.300E+03,
    2.325E+03,
    2.350E+03,
    2.375E+03,
    2.400E+03,
    2.425E+03,
    2.450E+03,
    2.475E+03,
    2.500E+03,
    2.525E+03,
    2.550E+03,
    2.575E+03,
    2.600E+03,
    2.625E+03,
    2.650E+03,
    2.675E+03,
    2.700E+03,
    2.725E+03,
    2.750E+03,
    2.775E+03,
    2.800E+03,
    2.825E+03,
    2.850E+03,
    2.875E+03,
    2.900E+03,
    2.925E+03,
    2.950E+03,
    2.975E+03,
    3.000E+03,
    3.025E+03,
    3.050E+03,
    3.075E+03,
    3.100E+03,
    3.125E+03,
    3.150E+03,
    3.175E+03,
    3.200E+03,
    3.225E+03,
    3.250E+03,
    3.275E+03,
    3.300E+03,
    3.325E+03,
    3.350E+03,
    3.375E+03,
    3.400E+03,
    3.425E+03,
    3.450E+03,
    3.475E+03,
    3.500E+03,
    3.525E+03,
    3.550E+03,
    3.575E+03,
    3.600E+03,
    3.625E+03,
    3.650E+03,
    3.675E+03,
    3.700E+03,
    3.725E+03,
    3.750E+03,
    3.775E+03,
    3.800E+03,
    3.825E+03,
    3.850E+03,
    3.875E+03,
    3.900E+03,
    3.925E+03,
    3.950E+03,
    3.975E+03};


    _hPyrolysisGas = {
    -7.247E+03,
    -7.208E+03,
    -7.170E+03,
    -7.130E+03,
    -7.090E+03,
    -7.049E+03,
    -7.006E+03,
    -6.963E+03,
    -6.917E+03,
    -6.870E+03,
    -6.821E+03,
    -6.770E+03,
    -6.715E+03,
    -6.657E+03,
    -6.595E+03,
    -6.527E+03,
    -6.451E+03,
    -6.365E+03,
    -6.266E+03,
    -6.148E+03,
    -6.005E+03,
    -5.827E+03,
    -5.608E+03,
    -5.338E+03,
    -5.014E+03,
    -4.638E+03,
    -4.219E+03,
    -3.775E+03,
    -3.335E+03,
    -2.936E+03,
    -2.605E+03,
    -2.353E+03,
    -2.170E+03,
    -2.034E+03,
    -1.925E+03,
    -1.830E+03,
    -1.741E+03,
    -1.653E+03,
    -1.561E+03,
    -1.454E+03,
    -1.316E+03,
    -1.144E+03,
    -9.480E+02,
    -7.397E+02,
    -5.255E+02,
    -3.087E+02,
    -9.028E+01,
    1.299E+02,
    3.533E+02,
    5.820E+02,
    8.187E+02,
    1.067E+03,
    1.329E+03,
    1.608E+03,
    1.905E+03,
    2.214E+03,
    2.527E+03,
    2.827E+03,
    3.098E+03,
    3.331E+03,
    3.529E+03,
    3.701E+03,
    3.853E+03,
    3.991E+03,
    4.119E+03,
    4.241E+03,
    4.356E+03,
    4.468E+03,
    4.576E+03,
    4.682E+03,
    4.786E+03,
    4.889E+03,
    4.991E+03,
    5.093E+03,
    5.195E+03,
    5.297E+03,
    5.399E+03,
    5.502E+03,
    5.605E+03,
    5.710E+03,
    5.816E+03,
    5.924E+03,
    6.033E+03,
    6.144E+03,
    6.257E+03,
    6.373E+03,
    6.491E+03,
    6.612E+03,
    6.736E+03,
    6.863E+03,
    6.994E+03,
    7.128E+03,
    7.267E+03,
    7.410E+03,
    7.558E+03,
    7.711E+03,
    7.870E+03,
    8.034E+03,
    8.204E+03,
    8.382E+03,
    8.566E+03,
    8.757E+03,
    8.957E+03,
    9.165E+03,
    9.382E+03,
    9.608E+03,
    9.844E+03,
    1.009E+04,
    1.035E+04,
    1.062E+04,
    1.090E+04,
    1.119E+04,
    1.150E+04,
    1.182E+04,
    1.216E+04,
    1.251E+04,
    1.288E+04,
    1.326E+04,
    1.366E+04,
    1.408E+04,
    1.452E+04,
    1.498E+04,
    1.546E+04,
    1.596E+04,
    1.648E+04,
    1.702E+04,
    1.758E+04,
    1.817E+04,
    1.877E+04,
    1.940E+04,
    2.005E+04,
    2.072E+04,
    2.140E+04,
    2.211E+04,
    2.283E+04,
    2.357E+04,
    2.432E+04,
    2.509E+04,
    2.587E+04,
    2.665E+04,
    2.745E+04,
    2.825E+04,
    2.906E+04,
    2.987E+04,
    3.068E+04,
    3.149E+04,
    3.230E+04,
    3.311E+04,
    3.391E+04,
    3.471E+04,
    3.550E+04,
    3.628E+04};

    _MuPyrolysisGas = {
    8.688E-02,
    9.666E-02,
    1.065E-01,
    1.162E-01,
    1.257E-01,
    1.351E-01,
    1.444E-01,
    1.534E-01,
    1.623E-01,
    1.710E-01,
    1.796E-01,
    1.879E-01,
    1.962E-01,
    2.042E-01,
    2.122E-01,
    2.201E-01,
    2.279E-01,
    2.356E-01,
    2.432E-01,
    2.509E-01,
    2.586E-01,
    2.664E-01,
    2.744E-01,
    2.826E-01,
    2.909E-01,
    2.993E-01,
    3.076E-01,
    3.157E-01,
    3.235E-01,
    3.309E-01,
    3.378E-01,
    3.444E-01,
    3.506E-01,
    3.566E-01,
    3.625E-01,
    3.683E-01,
    3.740E-01,
    3.797E-01,
    3.854E-01,
    3.913E-01,
    3.979E-01,
    4.053E-01,
    4.131E-01,
    4.212E-01,
    4.292E-01,
    4.371E-01,
    4.448E-01,
    4.521E-01,
    4.589E-01,
    4.651E-01,
    4.708E-01,
    4.757E-01,
    4.799E-01,
    4.832E-01,
    4.859E-01,
    4.880E-01,
    4.899E-01,
    4.920E-01,
    4.946E-01,
    4.981E-01,
    5.021E-01,
    5.066E-01,
    5.112E-01,
    5.160E-01,
    5.209E-01,
    5.258E-01,
    5.306E-01,
    5.355E-01,
    5.404E-01,
    5.453E-01,
    5.502E-01,
    5.550E-01,
    5.599E-01,
    5.647E-01,
    5.696E-01,
    5.744E-01,
    5.792E-01,
    5.840E-01,
    5.888E-01,
    5.936E-01,
    5.984E-01,
    6.032E-01,
    6.080E-01,
    6.128E-01,
    6.176E-01,
    6.224E-01,
    6.272E-01,
    6.320E-01,
    6.368E-01,
    6.416E-01,
    6.464E-01,
    6.512E-01,
    6.560E-01,
    6.609E-01,
    6.657E-01,
    6.705E-01,
    6.754E-01,
    6.803E-01,
    6.852E-01,
    6.901E-01,
    6.951E-01,
    7.000E-01,
    7.050E-01,
    7.100E-01,
    7.151E-01,
    7.201E-01,
    7.252E-01,
    7.304E-01,
    7.356E-01,
    7.408E-01,
    7.461E-01,
    7.514E-01,
    7.567E-01,
    7.621E-01,
    7.676E-01,
    7.731E-01,
    7.787E-01,
    7.843E-01,
    7.900E-01,
    7.957E-01,
    8.015E-01,
    8.074E-01,
    8.133E-01,
    8.193E-01,
    8.253E-01,
    8.313E-01,
    8.374E-01,
    8.436E-01,
    8.497E-01,
    8.558E-01,
    8.620E-01,
    8.680E-01,
    8.741E-01,
    8.801E-01,
    8.859E-01,
    8.917E-01,
    8.974E-01,
    9.029E-01,
    9.082E-01,
    9.133E-01,
    9.183E-01,
    9.230E-01,
    9.276E-01,
    9.319E-01,
    9.360E-01,
    9.398E-01,
    9.435E-01,
    9.469E-01,
    9.502E-01,
    9.532E-01,
    9.561E-01,
    9.587E-01};

    _MPyrolysisGas = {
    2.200E+01,
    2.200E+01,
    2.200E+01,
    2.200E+01,
    2.200E+01,
    2.200E+01,
    2.200E+01,
    2.199E+01,
    2.199E+01,
    2.199E+01,
    2.198E+01,
    2.197E+01,
    2.195E+01,
    2.192E+01,
    2.187E+01,
    2.180E+01,
    2.171E+01,
    2.159E+01,
    2.142E+01,
    2.119E+01,
    2.089E+01,
    2.050E+01,
    1.999E+01,
    1.937E+01,
    1.864E+01,
    1.784E+01,
    1.700E+01,
    1.619E+01,
    1.546E+01,
    1.486E+01,
    1.441E+01,
    1.412E+01,
    1.395E+01,
    1.385E+01,
    1.380E+01,
    1.378E+01,
    1.376E+01,
    1.375E+01,
    1.374E+01,
    1.371E+01,
    1.364E+01,
    1.353E+01,
    1.340E+01,
    1.326E+01,
    1.311E+01,
    1.297E+01,
    1.284E+01,
    1.271E+01,
    1.258E+01,
    1.246E+01,
    1.234E+01,
    1.222E+01,
    1.210E+01,
    1.198E+01,
    1.186E+01,
    1.173E+01,
    1.161E+01,
    1.150E+01,
    1.140E+01,
    1.132E+01,
    1.126E+01,
    1.121E+01,
    1.117E+01,
    1.114E+01,
    1.112E+01,
    1.110E+01,
    1.108E+01,
    1.107E+01,
    1.106E+01,
    1.105E+01,
    1.104E+01,
    1.103E+01,
    1.102E+01,
    1.102E+01,
    1.101E+01,
    1.101E+01,
    1.100E+01,
    1.100E+01,
    1.100E+01,
    1.099E+01,
    1.099E+01,
    1.098E+01,
    1.098E+01,
    1.097E+01,
    1.096E+01,
    1.096E+01,
    1.095E+01,
    1.094E+01,
    1.093E+01,
    1.092E+01,
    1.091E+01,
    1.090E+01,
    1.089E+01,
    1.088E+01,
    1.086E+01,
    1.085E+01,
    1.083E+01,
    1.081E+01,
    1.080E+01,
    1.077E+01,
    1.075E+01,
    1.073E+01,
    1.070E+01,
    1.067E+01,
    1.064E+01,
    1.061E+01,
    1.058E+01,
    1.054E+01,
    1.050E+01,
    1.046E+01,
    1.042E+01,
    1.037E+01,
    1.033E+01,
    1.027E+01,
    1.022E+01,
    1.016E+01,
    1.011E+01,
    1.004E+01,
    9.978E+00,
    9.910E+00,
    9.839E+00,
    9.766E+00,
    9.689E+00,
    9.610E+00,
    9.529E+00,
    9.444E+00,
    9.357E+00,
    9.268E+00,
    9.177E+00,
    9.085E+00,
    8.990E+00,
    8.894E+00,
    8.797E+00,
    8.699E+00,
    8.601E+00,
    8.503E+00,
    8.405E+00,
    8.307E+00,
    8.210E+00,
    8.113E+00,
    8.018E+00,
    7.925E+00,
    7.833E+00,
    7.743E+00,
    7.655E+00,
    7.569E+00,
    7.485E+00,
    7.404E+00,
    7.325E+00,
    7.248E+00,
    7.174E+00,
    7.103E+00};

    _GasEnthalpyInterpolant.set_points(_TemperaturesPyrolysisGas, _hPyrolysisGas);
    _GasViscosityInterpolant.set_points(_TemperaturesPyrolysisGas, _MuPyrolysisGas);
    _GasMolarWeightInterpolant.set_points(_TemperaturesPyrolysisGas, _MPyrolysisGas);


    return;
}

// end of file