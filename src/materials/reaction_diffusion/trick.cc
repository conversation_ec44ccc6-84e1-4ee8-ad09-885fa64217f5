#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>

#include "trick.h"
#include "../../mathlib/mathlib.h"
#include "../../io/summit_message.h"

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    0
// ****************************************************

summit::Trick::Trick(const std::string& name)
  : ReactionDiffusionMaterial(name, TRICK_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    _setInternalVariableMap();
}

summit::Trick::~Trick() {}

void summit::Trick::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);
    _Ai.resize(TRICK_NUMBER_INTERNAL_VARIABLES,0.0);
    _Ei.resize(TRICK_NUMBER_INTERNAL_VARIABLES,0.0);
    _M0i.resize(TRICK_NUMBER_INTERNAL_VARIABLES,0.0);
    _Dj.resize(TRICK_NUMBER_SPECIES,0.0);
    
    // initialization of member variables
    for (int a = 0; a < TRICK_NUMBER_REACTIONS;a++){
        _Ai[a] = values[a] / 60.0; // convert from rate per minute to rate per second
        _Ei[a] = values[a + TRICK_NUMBER_REACTIONS];
        _M0i[a] = values[a + 2 * TRICK_NUMBER_REACTIONS];
    }
    for (int a = 0; a < TRICK_NUMBER_SPECIES;a++){
        _Dj[a] = values[a + 3 * TRICK_NUMBER_REACTIONS];
    }

    _D = values[TRICK_NUMBER_SPECIES + 3 * TRICK_NUMBER_REACTIONS];
    // all done
    return;
}

void summit::Trick::Display()
{
    // Check material properties
    Message::Info("Trick material parameters:");
    Message::Info("\tDarcy Constant....... = %e", _D);
    // end of method
    return;
}

summit::real summit::Trick::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    // diffusivity
    //return _thermal_conductivity / (_rho * _heat_capacity);
    return 0.000000000001;
}

void summit::Trick::Constitutive(const real* concentration,
                                                           const real* u1,
                                                           const real* Dconcentration,
                                                           const real* Du1,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dtime,
                                                           const int ndf, // number of components in concentration
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{
    // calculation of the chemical diffusion
    for (int c = 0; c < ndm; c++){
        for (int dimension = 0; dimension < ndm; dimension++){
                // linear diffusion for all components of the gaseos species
            P[dimension + c * ndm] = _Dj[c] * Dconcentration[dimension + c * ndm];
        }
    }
    

    // end done
    return;
}


summit::real summit::Trick::capacity(real const* internal, int component) const
{
    // all done
    return 1000.0;// line for testing
    //return _internal[2]; //volume fraction
}

void summit::Trick::_setInternalVariableMap()
{

    SetLocationInInternalTable("Epsilon", 0, 13);
    
    // all done
    return;
}

void summit::Trick::Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const
{
    // create the rate constants
    real temperature = 1000;
    std::vector<real> K(TRICK_NUMBER_REACTIONS);
    for(int i = 0; i < TRICK_NUMBER_REACTIONS; i++){
        K[i] = _Ai[i] * std::exp(-_Ei[i] / (1.987e-3 * temperature));
    }

    f[0] = K[0] * (_M0i[0] - q[0]) + K[1] * (_M0i[1] - q[1]) + K[2] * (_M0i[2] - q[2]);
    f[1] = K[3] * (_M0i[3] - q[3]);
    f[2] = K[4] * (_M0i[4] - q[4]) + K[5] * (_M0i[5] - q[5]) + K[6] * (_M0i[6] - q[6]);
    f[3] = K[7] * (_M0i[7] - q[7]);
    f[4] = K[8] * (_M0i[8] - q[8]) + K[9] * (_M0i[9] - q[9]);
    f[5] = K[10] * (_M0i[10] - q[10]) + K[11] * (_M0i[11] - q[11]);
    f[6] = K[12] * (_M0i[12] - q[12]);

    //update extent of reaction
    for(int i = 0; i < TRICK_NUMBER_REACTIONS ; i++){
        q[i] = q[i] + K[i] * (_M0i[i] - q[i]) * dt[0];
    }

    
    // end done
    return;
}

void summit::Trick::ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{
    return;
}

int summit::Trick::number_unknowns() const {
    return 7;
}
// end of file
