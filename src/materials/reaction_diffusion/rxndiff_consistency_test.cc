/*
 * <PERSON> (<EMAIL>), 2023
 */

#include <cstdio>
#include <cmath>
#include <pyre/journal.h>
#include "rxndiff_consistency_test.h"
#include "../reaction_diffusion_material.h"
#if WITH_GTEST
    #include <gtest/gtest.h>
#endif

summit::ReactionDiffusionConsistencyTest::ReactionDiffusionConsistencyTest(const summit::ReactionDiffusionMaterial* mat,
                                                                           summit::real tol,
                                                                           summit::real perturbation,
                                                                           summit::real ndm,
                                                                           summit::real ndf)
    : _material(mat), _tol(tol), _perturbation(perturbation), _ndf(ndf), _ndm(ndm) {

        // Create a Pyre debug channel and deactivate it by default
        pyre::journal::debug_t info("summit.materials.reactiondiffusion.consistencytest.debug");
        info.deactivate();
    }

void summit::ReactionDiffusionConsistencyTest::TestdFdu(const real* u, const real* gradu, const real* q) {
    int dFdu_dim = _ndf * _ndf * _ndm;
    int dFdDu_dim = _ndf * _ndf * _ndm * _ndm;

    // Create a copy of q, u0, and gradu0 for constitutive call
    real u0[_ndf], gradu0[_ndf * _ndm], q0[_material->nInt()];
    std::copy(u, u + _ndf, u0);
    std::copy(gradu, gradu + (_ndf * _ndm), gradu0);
    std::copy(q, q + _material->nInt(), q0);

    // Calculate the analytical tangents
    real F[_ndf * _ndm]      = { 0. };
    real dFdu[dFdu_dim]      = { 0. };
    real dFdDu[dFdDu_dim] = { 0. };
    _material->Constitutive(u0, u, gradu0, gradu, F, q0, dFdDu, dFdu, 1e-3, _ndf, _ndm, true, false);

    // Calculate the numerical tangent
    real dFdu_num[dFdu_dim] = { 0. };
    _numerical_dFdu(u0, u, gradu0, gradu, q0, dFdu_num);

    // Print debug info if requested
    pyre::journal::debug_t info("summit.materials.reactiondiffusion.consistencytest.debug");
    info << "Provided tangent dFdu:" << pyre::journal::newline;
    for (int i = 0; i < dFdu_dim; i++) info << dFdu[i] << ", ";
    info << pyre::journal::newline << "Expected tangent dFdu:" << pyre::journal::newline;
    for (int i = 0; i < dFdu_dim; i++) info << dFdu_num[i] << ", ";
    info << pyre::journal::newline << pyre::journal::endl;

    // Check for Consistency in the stress
    for (int i = 0; i < dFdu_dim; i++) {
        #if WITH_GTEST
            ASSERT_NEAR(_error(dFdu[i], dFdu_num[i]), 0.0, _tol);
        #else
            pyre::journal::error_t error("summit.materials.reactiondiffusion.consistencytest.dFdu");
            if (this->_error(dFdu[i], dFdu_num[i]) > _tol)
                error << "Assertion failed." << pyre::journal::endl(__HERE__);
        #endif
    }
    return; // Done!
}

void summit::ReactionDiffusionConsistencyTest::TestdFdDu(const real* u, const real* gradu, const real* q) {
    int dFdu_dim = _ndf * _ndf * _ndm;
    int dFdDu_dim = _ndf * _ndf * _ndm * _ndm;

    // Create a copy of q, u0, and gradu0 for constitutive call
    real u0[_ndf], gradu0[_ndf * _ndm], q0[_material->nInt()];
    std::copy(u, u + _ndf, u0);
    std::copy(gradu, gradu + (_ndf * _ndm), gradu0);
    std::copy(q, q + _material->nInt(), q0);

    // Calculate the analytical tangents
    real F[_ndf * _ndm]   = { 0. };
    real dFdu[dFdu_dim]   = { 0. };
    real dFdDu[dFdDu_dim] = { 0. };
    _material->Constitutive(u0, u, gradu0, gradu, F, q0, dFdDu, dFdu, 1e-3, _ndf, _ndm, true, false);

    // Calculate the numerical tangent
    real dFdDu_num[dFdDu_dim] = { 0. };
    _numerical_dFdDu(u0, u, gradu0, gradu, q0, dFdDu_num);

    // Print debug info if requested
    pyre::journal::debug_t info("summit.materials.reactiondiffusion.consistencytest.debug");
    info << "Provided tangent dFdDu:" << pyre::journal::newline;
    for (int i = 0; i < dFdDu_dim; i++) info << dFdDu[i] << ", ";
    info << pyre::journal::newline << "Expected tangent dFdDu:" << pyre::journal::newline;
    for (int i = 0; i < dFdDu_dim; i++) info << dFdDu_num[i] << ", ";
    info << pyre::journal::newline << pyre::journal::endl;

    // Check for Consistency in the stress
    for (int i = 0; i < dFdDu_dim; i++) {
        #if WITH_GTEST
            ASSERT_NEAR(_error(dFdDu[i], dFdDu_num[i]), 0.0, _tol);
        #else
            pyre::journal::error_t error("summit.materials.reactiondiffusion.consistencytest.dFdDu");
            if (this->_error(dFdDu[i], dFdDu_num[i]) > _tol)
                error << "Assertion failed." << pyre::journal::endl(__HERE__);
        #endif
    }
    return; // Done!
}

void summit::ReactionDiffusionConsistencyTest::Testdrdu(const real* u, const real* q) {
    
    // Create a copy of q and u0 for constitutive call
    real u0[_ndf], q0[_material->nInt()];
    std::copy(u, u + _ndf, u0);
    std::copy(q, q + _material->nInt(), q0);

    // Calculate the analytical tangents
    real r[_ndf]           = { 0. };
    real drdu[_ndf * _ndf] = { 0. };
    real dt = 1e-3;
    _material->Source(u0, u, q0, &dt, r, drdu, _ndf);

    // Calculate the numerical tangent
    real drdu_num[_ndf * _ndf] = { 0. };
    _numerical_drdu(u0, u, q0, drdu_num);

    // Print debug info if requested
    pyre::journal::debug_t info("summit.materials.reactiondiffusion.consistencytest.debug");
    info << "Provided tangent drdu:" << pyre::journal::newline;
    for (int i = 0; i < (_ndf * _ndf); i++) info << drdu[i] << ", ";
    info << pyre::journal::newline << "Expected tangent drdu:" << pyre::journal::newline;
    for (int i = 0; i < (_ndf * _ndf); i++) info << drdu_num[i] << ", ";
    info << pyre::journal::newline << pyre::journal::endl;

    // Check for Consistency in the stress
    for (int i = 0; i < (_ndf * _ndf); i++) {
        #if WITH_GTEST
            ASSERT_NEAR(_error(drdu[i], drdu_num[i]), 0.0, _tol);
        #else
            pyre::journal::error_t error("summit.materials.reactiondiffusion.consistencytest.drdu");
            if (this->_error(drdu[i], drdu_num[i]) > _tol)
                error << "Assertion failed." << pyre::journal::endl(__HERE__);
        #endif
    }
    return; // Done!
}

void summit::ReactionDiffusionConsistencyTest::_numerical_dFdDu(const real* u0, 
                                                                const real* u,
                                                                const real* gradu0,
                                                                const real* gradu,
                                                                real* q,
                                                                real* dFdDu_num) const {
    // Allocate memory
    real Fp[_ndf * _ndm], Fm[_ndf * _ndm];
    real gradup[_ndf * _ndm], gradum[_ndf * _ndm];

    // Loop through dimensions
    for (int k = 0; k < _ndf; k++) {
        for (int l = 0; l < _ndm; l++) {
            int kl = _ndm * k + l;
            std::copy(gradu, gradu + (_ndf * _ndm), gradup);
            std::copy(gradu, gradu + (_ndf * _ndm), gradum);
            gradup[kl] += _perturbation;
            gradum[kl] -= _perturbation;

            _material->Constitutive(u0, u, gradu0, gradup, Fp, q, NULL, NULL, 1e-3, _ndf, _ndm, false, false);
            _material->Constitutive(u0, u, gradu0, gradum, Fm, q, NULL, NULL, 1e-3, _ndf, _ndm, false, false);
            
            for (int i = 0; i < _ndf; i++) {
                for (int j = 0; j < _ndm; j++) {
                    int ij = _ndm * i + j;
                    int ijkl = l + _ndm *(k + _ndf * (j + _ndm * i));
                    dFdDu_num[ijkl] = (Fp[ij] - Fm[ij]) / (2. * _perturbation);
                }
            }
        }
    }
    return; // All done
}

void summit::ReactionDiffusionConsistencyTest::_numerical_dFdu(const real* u0, 
                                                               const real* u,
                                                               const real* gradu0,
                                                               const real* gradu,
                                                               real* q,
                                                               real* dFdu_num) const {
    // Allocate memory
    real Fp[_ndf * _ndm], Fm[_ndf * _ndm];
    real up[_ndf], um[_ndf];

    // Loop through dimensions
    for (int k = 0; k < _ndf; k++) {
        std::copy(u, u + _ndf, up);
        std::copy(u, u + _ndf, um);
        up[k] += _perturbation;
        um[k] -= _perturbation;

        _material->Constitutive(u0, up, gradu0, gradu, Fp, q, NULL, NULL, 1e-3, _ndf, _ndm, false, false);
        _material->Constitutive(u0, um, gradu0, gradu, Fm, q, NULL, NULL, 1e-3, _ndf, _ndm, false, false);
        
        for (int i = 0; i < _ndf; i++) {
            for (int j = 0; j < _ndm; j++) {
                int ij = _ndm * i + j;
                int ijk = k + _ndf * (j + _ndm * i);
                dFdu_num[ijk] = (Fp[ij] - Fm[ij]) / (2. * _perturbation);
            }
        }
    }
    return; // All done
}

void summit::ReactionDiffusionConsistencyTest::_numerical_drdu(const real* u0, 
                                                               const real* u,
                                                               real* q,
                                                               real* drdu_num) const {
    // Allocate memory
    real rp[_ndf], rm[_ndf], up[_ndf], um[_ndf];
    real dt = 1e-3;

    // Loop through dimensions
    for (int k = 0; k < _ndf; k++) {
        std::copy(u, u + _ndf, up);
        std::copy(u, u + _ndf, um);
        up[k] += _perturbation;
        um[k] -= _perturbation;

        real drdu[_ndf * _ndf] = { 0. };
        _material->Source(u0, up, q, &dt, rp, drdu, _ndf);
        _material->Source(u0, um, q, &dt, rm, drdu, _ndf);
        
        for (int i = 0; i < _ndf; i++) {
            int ik = _ndf * i + k;
            drdu_num[ik] = (rp[i] - rm[i]) / (2. * _perturbation);
        }
    }
    return; // All done
}

real summit::ReactionDiffusionConsistencyTest::_error(real A, real A_num) {
    if (std::fabs(A) < 1.e-12)  // If A is too small
        return std::fabs(A-A_num);     // use absolute error
    else                        // Else
        return std::fabs((A-A_num)/A); // use relative error
}

// End of file
