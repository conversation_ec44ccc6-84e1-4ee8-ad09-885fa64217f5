#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>

#include "linear_piezo.h"
#include "../../mathlib/mathlib.h"
#include "../../io/summit_message.h"

#define NQ 9
// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    9
//      q[0,...,5] = Epsilon[0], ..., Epsilon[5] : Symmetric Elastic Strain Tensor
//      q[6,...,8] = E[0],...,E[2] : Electric Field
// ****************************************************

summit::LinearPiezo::LinearPiezo(const std::string& name)
  : ReactionDiffusionMaterial(name, NQ)
{
    // fill the map
    _setInternalVariableMap();
}

summit::LinearPiezo::~LinearPiezo() {}

void summit::LinearPiezo::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);

    // initialization of member variables
    // Density
    _rho = values[0];

    // Piezoelectric constants dijk (3 x 6 matrix)
    /*
    d11, d12, ..., d16
    d21, d22, ..., d26
    d31, d32, ..., d36
    */
    for (int i = 0; i < 18; i++){
        _d[i] = values[i + 1];
    }

    // Dielectric constants pij - enforce the symmetry here
    //¯\_(ツ)_/¯ the notation is weird (3 x 3 matrix)
    /*
    p11 p12 p13
    |       |
    p13 ... p33
    */
    _p[0] = values[19]; //p11
    _p[1] = _p[3] = values[20]; //p12
    _p[2] = _p[6] = values[21]; //p13

    _p[4] = values[22]; //p22
    _p[5] = _p[7] = values[23]; //p23

    _p[8] = values[24];

    // expect 1 + 18 + 6  = 25 parameters
    if (values.size() != 25) {
        // throw message and die
        throw std::runtime_error(
          "LinearPiezoModel::Load: I expect you to give me 3 input parameters in "
          "the "
          "material description file");
    }

    // Check material properties
    if (_rho < 0.e0) {
        Message::Fatal("In LinearPiezoModel: density %e must be a positive number",
                       _rho);
    }

    //Are there any other things that must be enforced? Maybe _p must be PD? 

    // all done
    return;
}

void summit::LinearPiezo::Display()
{
    // Check material properties
    Message::Info("Linear piezoelectric model");
    Message::Info("Piezoelectric Tensor:");

    for (int i = 0; i < 3; i++){
        for (int j = 0; j < 6; j++){
            std::cout << _d[6 * i + j] << ' '; 
        }
        std::cout << std::endl;
    }

    Message::Info("Dielectric Tensor:");

    for (int i = 0; i < 3; i++){
        for (int j = 0; j < 3; j++){
            std::cout << _p[3 * i + j] << ' '; 
        }
        std::cout << std::endl;
    }
    // end of method
    return;
}

summit::real summit::LinearPiezo::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    // diffusivity
    //return _thermal_conductivity / (_rho * _heat_capacity);
    //this is important for stabilizing the DG elements (I think)
    return 0.0;
}

void summit::LinearPiezo::Constitutive(const real* V0,
                                            const real* V,
                                            const real* E0,
                                            const real* E,
                                            real* P,
                                            real* q,
                                            real* tangent,
                                            real* dPdu,
                                            real dtime,
                                            const int ndf,
                                            const int ndm,
                                            bool compute_tangents,
                                            bool artVisc_interface_activate) const
{
    // From classical linear piezoelectric theory:
    // D_i = d_ijk*epsilon_jk + p_ij*E_j

    real e[3] = {0.}; //3D electric field used for calculations
    real strain[6] = {0.}; //infinitesimal strain from mechanics (Voigt notation!)
    real P_aux[3] = {0.}; // 3D electric displacement

    // Move up in dimension if necessary
    for (int i = 0; i < ndm; i++){
        e[i] = E[i];
    }

    for (int i = 0; i < 3; i++){
        // Put the electric field into the internal table to give to the mechanics system
        // We always assume that the field is 3D for the purposes of transfer
        // We also flip the sign so that the mechanics problem gets the physically correct E-field
        q[i + 6] = -1.0 * e[i];
    }

    for (int i = 0; i < 6; i++){
        //Load strain from internal table from mechanics system
        //Similarly always assume the full 3D strain is being transferred even for 2D problems
        strain[i] = q[i];
    }

    //Do the electro-mechanical coupling dijk * epsilon_jk
    // We have to flip the sign here because we want -D
    // Di = dijk * eps_jk + pij * E^real_j (where these are the physical quantities)
    // -Di = -dijk * eps_jk - pij* E^real_j
    // -Di = -dijk * eps_jk + pij * ej (ej = -E^real_j)
    for (int i = 0; i < 3; i ++){
        for (int j = 0; j < 6; j++){
            P_aux[i] -= _d[6 * i + j] * strain[j];
        }
    }

    //Now do the electric field contribution, pij*Ej
    // Remember that e is the negative of the true E, so it is added here to get -D

    for (int i = 0; i < 3; i++){
        for (int j = 0; j < 3; j++){
            P_aux[i] += _p[3 * i + j] * e[j];
        }
    }

    //Project electric displacement and tangents into 2D if necessary
    // In the linear theory, the tangents (d^2 W/dE_idE_j) is just p_ij (dielectric constants)
    for (int i = 0; i < ndm; i++){
        P[i] = P_aux[i];
        for (int j = 0; j < ndm; j++){
            tangent[ndm * i + j] = _p[3 * i + j];
        }
    }

    // end done
    return;
}

void summit::LinearPiezo::_setInternalVariableMap()
{
    SetLocationInInternalTable("Elastic Strain", 0, 6);
    SetLocationInInternalTable("Electric Field", 6, 3);
    return;
}

summit::real summit::LinearPiezo::capacity(real const* internal, const int component) const
{
    return 0.0;//_heatCapacity * _rho;    
}

void summit::LinearPiezo::Source(const real* V0, const real* V, const real* DV0, const real* DV, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const
{   
    f[0] = 0.0; //_rho; add source terms here! tbd
    df[0] = 0.0;
    return;
}

void summit::LinearPiezo::ConvectiveFlux(const real* V0, const real* V, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{
    return;
}

int summit::LinearPiezo::number_unknowns() const
{
    return 1;
}

real summit::LinearPiezo::bulkModulus(real const* internal) const
{
    //Figure out the appropriate value
    return std::max(_p[0], std::max(_p[4], _p[8]));
}
// end of file
