/**
 * @file electro_elastic.h
 * <AUTHOR> pickard (<EMAIL>), a<PERSON><PERSON> rau, chris quinn
 * @brief A material model for coupled electro-elastic phenomena
 * 
 * This file implements a material model for coupled electro-elastic phenomena,
 * including piezoelectric, electrostrictive, and electroactive materials where
 * mechanical deformation and electric fields interact.
 */

#ifndef SUMMIT_ELECTRO_ELASTIC_H
#define SUMMIT_ELECTRO_ELASTIC_H

#include "../reaction_diffusion_material.h"

// Number of internal variables:
// the value is used to size the memory allocation for the material ElectroElastic
                                         // 0 homogeneous reactions
#define ELECTRO_ELASTIC_NUMBER_INTERNAL_VARIABLES 12

namespace summit {

/**
 * @brief Implementation of a coupled electro-elastic material model
 * 
 * @details This class implements a material model for coupled electro-elastic phenomena,
 * where mechanical deformation and electric fields interact. The model accounts for:
 * 
 * - Linear and nonlinear piezoelectric effects
 * - Electrostrictive behavior
 * - Dielectric permittivity
 * - Elastic deformation
 * - Coupled field interactions
 * 
 * The governing equations couple the mechanical equilibrium and electrostatic equations:
 * 
 * \f[ \nabla \cdot \sigma = 0 \f]
 * \f[ \nabla \cdot D = \rho_e \f]
 * 
 * with constitutive relations:
 * 
 * \f[ \sigma_{ij} = C_{ijkl} \varepsilon_{kl} - e_{kij} E_k \f]
 * \f[ D_i = e_{ikl} \varepsilon_{kl} + \kappa_{ij} E_j \f]
 * 
 * where \f$\sigma\f$ is the stress tensor, \f$\varepsilon\f$ is the strain tensor,
 * \f$E\f$ is the electric field, \f$D\f$ is the electric displacement,
 * \f$C\f$ is the elasticity tensor, \f$e\f$ is the piezoelectric coupling tensor,
 * and \f$\kappa\f$ is the dielectric permittivity tensor.
 * 
 * Applications include:
 * - Piezoelectric sensors and actuators
 * - Smart materials and structures
 * - Energy harvesting devices
 * - Ultrasonic transducers
 */
class ElectroElastic : public ReactionDiffusionMaterial {
  public:

    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    ElectroElastic(const std::string& name);

    /**
     * Destructor
     */
    virtual ~ElectroElastic();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    ElectroElastic(const ElectroElastic&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    ElectroElastic& operator=(const ElectroElastic&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * ElectroElastic constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* concentration,
                              const real* u1,
                              const real* Dconcentration,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const;


    /**
     * set the source term
     */
    void ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;

    /**
     * Compute the bulk modulus
     */
    real bulkModulus(real const* internal) const;//this should be made virtual and reimplemented in future
    // also change to conductivity or something more sensible 
  
  protected: 

    /**
     * final mass fraction
     */
    real _rho;

    /**
     * solid thermal conductivity
     */
    real _C1;

    /**
     * first term
     */
    real _C2;

    /**
     * first term
     */
    real _mu;

    /**
     * first term
     */
    real _lambda;    

  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
