#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>

#include "lin_conv_diff.h"
#include "../../mathlib/mathlib.h"
#include "../../io/summit_message.h"

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    0
// ****************************************************

summit::LinearConvectionDiffusion::LinearConvectionDiffusion(const std::string& name)
  : ReactionDiffusionMaterial(name, 1)
{
    // fill the map
    _setInternalVariableMap();
}

summit::LinearConvectionDiffusion::~LinearConvectionDiffusion() {}

void summit::LinearConvectionDiffusion::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);

    _rhoC = values[0];

    _S = values[1];

    _D = values[2];

    _ux = values[3];

    _uy = values[4];

    // all done
    return;
}

void summit::LinearConvectionDiffusion::Display()
{
    // Check material properties
    Message::Info("Linear Convection Diffusion Model:");
    Message::Info("\tDensity Times Heat Capacity....... = %e", _rhoC);
    Message::Info("\tSource....... = %e", _S);
    Message::Info("\tDiffusion Constant....... = %e", _D);
    Message::Info("\tX Velocity....... = %e", _ux);
    Message::Info("\tY Velocity....... = %e", _uy);  
    
    // end of method
    return;
}

summit::real summit::LinearConvectionDiffusion::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    // diffusivity
    //return _thermal_conductivity / (_rho * _heat_capacity);
    //this is important for stabilizing the DG elements (I think)
    return _D / _rhoC;
}

void summit::LinearConvectionDiffusion::Constitutive(const real* concentration0,
                                                           const real* concentration,
                                                           const real* Dconcentration0,
                                                           const real* Dconcentration,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dtime,
                                                           const int ndf, // number of components in concentration
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{
    //std::cout << "K is: " << K <<std::endl;
    for (int component = 0; component < this->number_unknowns(); component++){
        for (int dimension = 0; dimension < ndm; dimension++){
            P[dimension + component * ndm] = (_D*(component+1)) * Dconcentration[dimension + component * ndm];
            if(compute_tangents){
                tangent[(dimension + component * ndm) * (ndm * ndf) + dimension + component * ndm] = (_D*(component+1));
            }
        }
    }

    // this sets the temperature in the internals for transfer to the solid!
    q[0] = concentration[0];

    return;
}


summit::real summit::LinearConvectionDiffusion::capacity(real const* internal, const int component) const
{
    return 0.0;    
}

void summit::LinearConvectionDiffusion::_setInternalVariableMap()
{   
    // all done
    SetLocationInInternalTable("Temperature", 0, 1);
    return;
}

void summit::LinearConvectionDiffusion::Source(const real* concentration0, const real* concentration, real* q, real* dt, real* f, real* df, size_t ndf) const
{   
    f[0] = _S - _rhoC / dt[0] * (concentration[0] - concentration0[0]);
    df[0] = - _rhoC / dt[0];
    // These lines allow us to generalize to do more than one component in the problem!
    // f[1] = _S - _rhoC / dt[0] * (concentration[1] - concentration0[1]);
    // df[4] = - _rhoC / dt[0];
    // f[2] = _S - _rhoC / dt[0] * (concentration[2] - concentration0[2]);
    // df[8] = - _rhoC / dt[0];
    //df[0] = - _rho * _heatCapacity / dt[0];
    return;
}

/**
 * @brief Compute convective flux for linear convection-diffusion
 *
 * Computes the convective flux F = u·c where u is the constant velocity
 * field and c is the concentration. This represents the transport of the
 * scalar quantity by the fluid motion.
 *
 * @param[in] concentration0 Previous time step concentration
 * @param[in] concentration Current concentration
 * @param[in] q Internal variables (unused)
 * @param[in] dt Time step information
 * @param[out] F Computed convective flux
 * @param[out] dF Flux derivatives (for implicit schemes)
 * @param[in] ndf Number of degrees of freedom (should be 1)
 * @param[in] ndm Number of spatial dimensions
 */
void summit::LinearConvectionDiffusion::ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{
    F[0] = _ux * concentration0[0];
    F[1] = _uy * concentration0[0];
    // These lines allow us to generalize to do more than one component in the problem!
    // F[2] = -_ux * concentration0[1];
    // F[3] = -_uy * concentration0[1];
    return;
}

int summit::LinearConvectionDiffusion::number_unknowns() const
{
    return 1;
}

real summit::LinearConvectionDiffusion::bulkModulus(real const* internal) const
{
    return _D;
}
// end of file
