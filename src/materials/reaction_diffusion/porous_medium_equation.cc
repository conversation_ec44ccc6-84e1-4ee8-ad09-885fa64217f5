#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>

#include "porous_medium_equation.h"
#include "../../mathlib/mathlib.h"
#include "../../io/summit_message.h"

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    0
// ****************************************************

summit::PorousMediumEquation::PorousMediumEquation(const std::string& name)
  : ReactionDiffusionMaterial(name, 5)
{
    // fill the map
    _setInternalVariableMap();
}

summit::PorousMediumEquation::~PorousMediumEquation() {}

void summit::PorousMediumEquation::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);

    _m = values[0];
    _rho = 1.0;

    // all done
    return;
}

void summit::PorousMediumEquation::Display()
{
    // Check material properties
    Message::Info("Porous Medium Equation Model:");
    Message::Info("\tCoefficient M..... = %e", _m); 
    
    // end of method
    return;
}

summit::real summit::PorousMediumEquation::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    // diffusivity
    //return _thermal_conductivity / (_rho * _heat_capacity);
    //this is important for stabilizing the DG elements (I think)
    return 1.0;
}

summit::real summit::PorousMediumEquation::_T_from_phi(const real phi) const
{
    // diffusivity
    //return _thermal_conductivity / (_rho * _heat_capacity);
    //this is important for stabilizing the DG elements (I think)
    real T = phi;
    real error = this->_phi_from_T(T) - phi;
    //std::cout << "error is: " << error << std::endl;
    while (std::abs(error)>1e-12 * phi){
        T -= error / this->_conductivity(T);
        error = this->_phi_from_T(T) - phi;
        //std::cout << "error is: " << error << std::endl;
    }
    return T;
}

summit::real summit::PorousMediumEquation::_phi_from_T(const real T) const
{   
    //integral of the conductivity
    return std::pow(T,_m);
}

summit::real summit::PorousMediumEquation::_conductivity(const real T) const
{   
    //derivative of the _phi_from_T function
    return _m * std::pow(T,_m - 1.0);
}
summit::real summit::PorousMediumEquation::_dconductivity(const real T) const
{   
    //derivative of the _phi_from_T function
    return _m * (_m - 1.0) * std::pow(T,_m - 2.0);
}


void summit::PorousMediumEquation::Constitutive(const real* concentration0,
                                                           const real* concentration,
                                                           const real* Dconcentration0,
                                                           const real* Dconcentration,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dtime,
                                                           const int ndf, // number of components in concentration
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{
    
    for (int component = 0; component < this->number_unknowns(); component++){
        for (int dimension = 0; dimension < ndm; dimension++){
            P[dimension + component * ndm] = Dconcentration[dimension + component * ndm];
            if(compute_tangents){
                tangent[(dimension + component * ndm) * (ndm * ndf) + dimension + component * ndm] = 1.0;
            }
            q[1+dimension] = Dconcentration[dimension + component * ndm];
        }
    }
    return;
}


summit::real summit::PorousMediumEquation::capacity(real const* internal, const int component) const
{
    return 0.0;
}

void summit::PorousMediumEquation::_setInternalVariableMap()
{   
    // all done
    SetLocationInInternalTable("Temperature", 0, 1);
    SetLocationInInternalTable("Heat_Flux", 1, 3);
    SetLocationInInternalTable("Thermal_Conductivity", 4, 1);
    return;
}

void summit::PorousMediumEquation::Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const
{   if (concentration[0]==0){
        return;
    }
    real phi = concentration[0];
    real phi0 = concentration0[0];
    real T = this->_T_from_phi(phi);
    q[0] = T;
    real T0 = this->_T_from_phi(phi0);
    real K =this->_conductivity(T);
    f[0] = - 1.0/ dt[0] * (phi - phi0) / K;
    q[4] = K;
    
    
    df[0] = - 1.0/ dt[0] / q[4];
    df[0] += 1.0/ dt[0] * (phi - phi0) / std::pow(K, 2.0) * (this->_dconductivity(T) / K);
    return ;
}

void summit::PorousMediumEquation::ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{
    return;
}

int summit::PorousMediumEquation::number_unknowns() const
{
    return 1;
}

real summit::PorousMediumEquation::bulkModulus(real const* internal) const
{
    return 1.0;
}
// end of file
