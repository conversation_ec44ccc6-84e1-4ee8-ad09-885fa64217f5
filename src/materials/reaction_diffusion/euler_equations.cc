#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>

#include "euler_equations.h"
#include "../../mathlib/mathlib.h"
#include "../../io/summit_message.h"

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    0
// ****************************************************

summit::EulerEquations::EulerEquations(const std::string& name)
  : ReactionDiffusionMaterial(name, EULER_EQUATIONS_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    _setInternalVariableMap();
}

summit::EulerEquations::~EulerEquations() {}

void summit::EulerEquations::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);
    
    _gamma = values[0];
    _lax_parameter = values[1];
    _implicit = (bool)values[2];
    _spatial_dimension = (int)values[3];
    
    // all done
    return;
}

void summit::EulerEquations::Display()
{
    // Check material properties
    Message::Info("Euler Equations Gas Dynamics Model:");
    Message::Info("\tGamma....... = %e", _gamma);
    Message::Info("\tLax Stabilization........ = %e", _lax_parameter);
    Message::Info("\tImplicit........ = %d", _implicit);
    Message::Info("\tDimension........ = %d", _spatial_dimension);
    // end of method
    return;
}

summit::real summit::EulerEquations::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    // diffusivity
    //return _thermal_conductivity / (_rho * _heat_capacity);
    //this is important for stabilizing the DG elements (I think)
    return 0.0;
}

void summit::EulerEquations::Constitutive(const real* concentration0,
                                                           const real* concentration,
                                                           const real* Dconcentration0,
                                                           const real* Dconcentration,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dtime,
                                                           const int ndf, // number of components in concentration
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{
    for (int component = 0; component < this->number_unknowns(); component++){
        for (int dimension = 0; dimension < ndm; dimension++){
            P[dimension + component * ndm] = 0.0;
            if(compute_tangents){
                tangent[(dimension + component * ndm) * (ndm * ndf) + dimension + component * ndm] = 0.0;
            }
        }
    }

    return;
}


summit::real summit::EulerEquations::capacity(real const* internal, const int component) const
{
    return 0.0;  
}

void summit::EulerEquations::_setInternalVariableMap()
{   
    // SetLocationInInternalTable("Gas Pressure", 0, 1);
    // SetLocationInInternalTable("Vx", 1, 1);
    // SetLocationInInternalTable("Vy", 2, 1);
    // all done
    return;
}

/**
 * @brief Compute source terms for Euler equations
 *
 * This method computes source terms for the Euler equations. For pure fluid
 * dynamics, source terms are typically zero, but this method can be extended
 * to include body forces, chemical reactions, or other source contributions.
 *
 * @param[in] concentration0 Previous time step concentration values
 * @param[in] concentration Current concentration values
 * @param[in] q Internal variables
 * @param[in] dt Time step information
 * @param[out] f Computed source terms
 * @param[out] df Source term Jacobian (for implicit schemes)
 * @param[in] ndf Number of degrees of freedom per node
 *
 * @pre concentration must contain valid fluid state variables
 * @pre f and df must be properly allocated
 *
 * @note Currently implements zero source terms for pure Euler equations
 */
void summit::EulerEquations::Source(const real* concentration0, const real* concentration, real* q, real* dt, real* f, real* df, size_t ndf) const
{
    if(_implicit){
        // std::cout << "evaluating sources implicitly" << std::endl;
        if(dt[0]<1e-15){
            std::cout << "time step: " << dt[0] << "too small. returning" <<std::endl;
            return;
        }
        real _rhoC = 1.0;
        // std::cout <<"(concentration[0] - concentration0[0]): "<< (concentration[0] - concentration0[0]) <<std::endl;
        // std::cout <<"(concentration[1] - concentration0[1]): "<< (concentration[1] - concentration0[1]) <<std::endl;
        // std::cout <<"(concentration[2] - concentration0[2]): "<< (concentration[2] - concentration0[2]) <<std::endl;
        // std::cout <<"(concentration[3] - concentration0[3]): "<< (concentration[3] - concentration0[3]) <<std::endl;
        f[0] = - _rhoC / dt[0] * (concentration[0] - concentration0[0]);
        df[0] = - _rhoC / dt[0];
        f[1] = - _rhoC / dt[0] * (concentration[1] - concentration0[1]);
        df[5] = - _rhoC / dt[0];
        f[2] = - _rhoC / dt[0] * (concentration[2] - concentration0[2]);
        df[10] = - _rhoC / dt[0];
        f[3] = - _rhoC / dt[0] * (concentration[3] - concentration0[3]);
        df[15] = - _rhoC / dt[0];
    }
    return;
    // for (int c=0;c<this->number_unknowns();c++){
    //     f[c] = - 1.0 / dt[0] * (concentration[c] - concentration0[c]);
    //     df[c*number_unknowns() + c] = - 1.0 / dt[0];
    // }
    // return;
}

/**
 * @brief Compute convective flux for Euler equations
 *
 * This method computes the convective flux terms for the Euler equations.
 * The flux includes contributions from mass, momentum, and energy transport
 * in compressible flow.
 *
 * @param[in] concentration0 Previous time step concentration values
 * @param[in] concentration Current concentration values (density, momentum, energy)
 * @param[in] q Internal variables
 * @param[in] dt Time step information
 * @param[out] F Computed convective flux tensor
 * @param[out] dF Flux Jacobian matrix (for implicit schemes)
 * @param[in] ndf Number of degrees of freedom per node
 * @param[in] ndm Number of spatial dimensions
 *
 * @pre concentration must contain valid fluid state variables
 * @pre F and dF must be properly allocated
 * @pre ndf should be 4 for Euler equations (ρ, ρu, ρv, ρE)
 *
 * @note The flux computation includes pressure calculations from equation of state
 */
void summit::EulerEquations::ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{
    // real _ux = 1.0;
    // real _uy = 0.0;
    // F[0] = _ux * concentration0[0];
    // F[1] = _uy * concentration0[0];
    // F[2] = -_ux * concentration0[1];
    // F[3] = -_uy * concentration0[1];
    // F[4] = _ux * concentration0[2];
    // F[5] = _uy * concentration0[2];
    // F[6] = -_ux * concentration0[3];
    // F[7] = -_uy * concentration0[3];
    // return;
    if(dt[0]<1e-15){
        std::cout << "time step: " << dt[0] << "too small. returning" <<std::endl;
        return;
    }
    
    real rho = concentration[0];
    real uv = concentration[1]/rho;
    real vv = concentration[2]/rho;
    real p = (_gamma-1.0) * (concentration[3] - 0.5 *rho*(uv*uv+vv*vv));

    F[0] = uv * rho;
    F[1] = vv * rho;

    F[2] = uv * uv * rho + p;
    F[3] = uv * vv * rho;
    F[4] = uv * vv * rho;
    F[5] = vv * vv * rho + p;
    
    F[6] = uv * (concentration[3]+p);
    F[7] = vv * (concentration[3]+p);
    //std::cout << F[0] << ", "
    // << F[1] << ", "
    // << F[2] << ", "
    // << F[3] << ", "
    // << F[4] << ", "
    // << F[5] << ", "
    // << F[6] << ", "
    // << F[7] << ", "
    // << std::endl;
    return;



    real vNorm2=0.0;
    real vComponent;
    for(int dim = 0; dim< _spatial_dimension;dim++){
        vComponent = concentration0[1+dim]/concentration0[0];
        vNorm2 += vComponent*vComponent;
        q[dim+1] = vComponent;
    }
    real pressure = (_gamma-1.0)*(concentration0[_spatial_dimension+1] - 0.5*concentration0[0]*vNorm2 );
    q[0] = pressure;

    for(int dim = 0; dim< _spatial_dimension;dim++){
        //F[comp * _spatial_dimension + dim]
        F[0 * _spatial_dimension + dim] = concentration0[1+dim]; //mass flux is the momentum vector
        for(int dim2 = 0; dim2< _spatial_dimension;dim2++){
            //JxJ contribution to momentum flux
            F[(1+dim2) * _spatial_dimension + dim] = concentration0[1+dim] * concentration0[1+dim2]/concentration0[0];
        }
        F[(1+dim) * _spatial_dimension + dim] += pressure; //add in the pressure term
        F[(1+_spatial_dimension) * _spatial_dimension + dim] = (concentration0[2+_spatial_dimension] + pressure)/ concentration0[0] * concentration0[1+dim];//energy flux
    }
    return ;
}

int summit::EulerEquations::number_unknowns() const
{
    return 4;
    // if(_spatial_dimension>2.5){
    //     return 5;
    // }
    // return 4;
}

real summit::EulerEquations::bulkModulus(real const* internal) const
{
    return 0;
}
// end of file
