#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>

#include "euler_equations.h"
#include "../../mathlib/mathlib.h"
#include "../../io/summit_message.h"

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    0
// ****************************************************

summit::EulerEquations::EulerEquations(const std::string& name)
  : ReactionDiffusionMaterial(name, EULER_EQUATIONS_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    _setInternalVariableMap();
}

summit::EulerEquations::~EulerEquations() {}

void summit::EulerEquations::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);
    
    _gamma = values[0];
    _lax_parameter = values[1];
    _implicit = (bool)values[2];
    _spatial_dimension = (int)values[3];
    
    // all done
    return;
}

void summit::EulerEquations::Display()
{
    // Check material properties
    Message::Info("Euler Equations Gas Dynamics Model:");
    Message::Info("\tGamma....... = %e", _gamma);
    Message::Info("\tLax Stabilization........ = %e", _lax_parameter);
    Message::Info("\tImplicit........ = %d", _implicit);
    Message::Info("\tDimension........ = %d", _spatial_dimension);
    // end of method
    return;
}

summit::real summit::EulerEquations::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    // diffusivity
    //return _thermal_conductivity / (_rho * _heat_capacity);
    //this is important for stabilizing the DG elements (I think)
    return 0.0;
}

void summit::EulerEquations::Constitutive(const real* concentration0,
                                                           const real* concentration,
                                                           const real* Dconcentration0,
                                                           const real* Dconcentration,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dtime,
                                                           const int ndf, // number of components in concentration
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{
    for (int component = 0; component < this->number_unknowns(); component++){
        for (int dimension = 0; dimension < ndm; dimension++){
            P[dimension + component * ndm] = 0.0;
            if(compute_tangents){
                tangent[(dimension + component * ndm) * (ndm * ndf) + dimension + component * ndm] = 0.0;
            }
        }
    }

    return;
}


summit::real summit::EulerEquations::capacity(real const* internal, const int component) const
{
    return 0.0;  
}

void summit::EulerEquations::_setInternalVariableMap()
{   
    // SetLocationInInternalTable("Gas Pressure", 0, 1);
    // SetLocationInInternalTable("Vx", 1, 1);
    // SetLocationInInternalTable("Vy", 2, 1);
    // all done
    return;
}

void summit::EulerEquations::Source(const real* concentration0, const real* concentration, real* q, real* dt, real* f, real* df, size_t ndf) const
{   
    if(_implicit){
        // std::cout << "evaluating sources implicitly" << std::endl;
        if(dt[0]<1e-15){
            std::cout << "time step: " << dt[0] << "too small. returning" <<std::endl;
            return;
        }
        real _rhoC = 1.0;
        // std::cout <<"(concentration[0] - concentration0[0]): "<< (concentration[0] - concentration0[0]) <<std::endl;
        // std::cout <<"(concentration[1] - concentration0[1]): "<< (concentration[1] - concentration0[1]) <<std::endl;
        // std::cout <<"(concentration[2] - concentration0[2]): "<< (concentration[2] - concentration0[2]) <<std::endl;
        // std::cout <<"(concentration[3] - concentration0[3]): "<< (concentration[3] - concentration0[3]) <<std::endl;
        f[0] = - _rhoC / dt[0] * (concentration[0] - concentration0[0]);
        df[0] = - _rhoC / dt[0];
        f[1] = - _rhoC / dt[0] * (concentration[1] - concentration0[1]);
        df[5] = - _rhoC / dt[0];
        f[2] = - _rhoC / dt[0] * (concentration[2] - concentration0[2]);
        df[10] = - _rhoC / dt[0];
        f[3] = - _rhoC / dt[0] * (concentration[3] - concentration0[3]);
        df[15] = - _rhoC / dt[0];
    }
    return;
    // for (int c=0;c<this->number_unknowns();c++){
    //     f[c] = - 1.0 / dt[0] * (concentration[c] - concentration0[c]);
    //     df[c*number_unknowns() + c] = - 1.0 / dt[0];
    // }
    // return;
}

void summit::EulerEquations::ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{   
    // real _ux = 1.0;
    // real _uy = 0.0;
    // F[0] = _ux * concentration0[0];
    // F[1] = _uy * concentration0[0];
    // F[2] = -_ux * concentration0[1];
    // F[3] = -_uy * concentration0[1];
    // F[4] = _ux * concentration0[2];
    // F[5] = _uy * concentration0[2];
    // F[6] = -_ux * concentration0[3];
    // F[7] = -_uy * concentration0[3];
    // return;
    if(dt[0]<1e-15){
        std::cout << "time step: " << dt[0] << "too small. returning" <<std::endl;
        return;
    }
    
    real rho = concentration[0];
    real uv = concentration[1]/rho;
    real vv = concentration[2]/rho;
    real p = (_gamma-1.0) * (concentration[3] - 0.5 *rho*(uv*uv+vv*vv));

    F[0] = uv * rho;
    F[1] = vv * rho;

    F[2] = uv * uv * rho + p;
    F[3] = uv * vv * rho;
    F[4] = uv * vv * rho;
    F[5] = vv * vv * rho + p;
    
    F[6] = uv * (concentration[3]+p);
    F[7] = vv * (concentration[3]+p);
    //std::cout << F[0] << ", "
    // << F[1] << ", "
    // << F[2] << ", "
    // << F[3] << ", "
    // << F[4] << ", "
    // << F[5] << ", "
    // << F[6] << ", "
    // << F[7] << ", "
    // << std::endl;
    return;



    real vNorm2=0.0;
    real vComponent;
    for(int dim = 0; dim< _spatial_dimension;dim++){
        vComponent = concentration0[1+dim]/concentration0[0];
        vNorm2 += vComponent*vComponent;
        q[dim+1] = vComponent;
    }
    real pressure = (_gamma-1.0)*(concentration0[_spatial_dimension+1] - 0.5*concentration0[0]*vNorm2 );
    q[0] = pressure;

    for(int dim = 0; dim< _spatial_dimension;dim++){
        //F[comp * _spatial_dimension + dim]
        F[0 * _spatial_dimension + dim] = concentration0[1+dim]; //mass flux is the momentum vector
        for(int dim2 = 0; dim2< _spatial_dimension;dim2++){
            //JxJ contribution to momentum flux
            F[(1+dim2) * _spatial_dimension + dim] = concentration0[1+dim] * concentration0[1+dim2]/concentration0[0];
        }
        F[(1+dim) * _spatial_dimension + dim] += pressure; //add in the pressure term
        F[(1+_spatial_dimension) * _spatial_dimension + dim] = (concentration0[2+_spatial_dimension] + pressure)/ concentration0[0] * concentration0[1+dim];//energy flux
    }
    return ;
}

int summit::EulerEquations::number_unknowns() const
{
    return 4;
    // if(_spatial_dimension>2.5){
    //     return 5;
    // }
    // return 4;
}

real summit::EulerEquations::bulkModulus(real const* internal) const
{
    return 0;
}

void summit::EulerEquations::upwindStabilization(const real* concentration_L, const real* concentration_L0, const real* concentration_R, const real* concentration_R0, const real* Normal, real* C, real* dC) const
{   
    double gam1 = _gamma - 1.0;

    double nx = Normal[0];
    double ny = Normal[1];

    double rr = concentration_L[0];
    double rum = concentration_L[1];
    double rvr = concentration_L[2];
    double rEr = concentration_L[3];

    double rl = concentration_R[0];
    double rup = concentration_R[1];
    double rvl = concentration_R[2];
    double rEl = concentration_R[3];
    double rr1 = 1.0 / rr;
    double um = rum * rr1;
    double vr = rvr * rr1;
    double Er = rEr * rr1;
    double u2r = um * um + vr * vr;
    double pr = gam1*(rEr - 0.5 * rr * u2r);
    double hr = Er + pr * rr1;
    double unr = um * nx + vr * ny;
    
    double rl1 = 1.0/rl;
    double up = rup * rl1;
    double vl = rvl * rl1;
    double El = rEl * rl1;
    double u2l = up * up + vl * vl;
    double pl = gam1 * (rEl - 0.5 * rl * u2l);

    double hl = El + pl * rl1;
    double unl = up * nx + vl * ny;

    double di = sqrt(rr * rl1);
    double dl = 1.0/ (di + 1.0);
    double ui = (di * um + up)*dl;
    double vi = (di * vr + vl)*dl;
    double hi = (di * hr + hl)*dl;
    double af = 0.5 * (ui * ui + vi * vi);
    double ci2 = gam1*(hi - af);
    double ci = sqrt(ci2);
    double uni = ui * nx + vi * ny;

    double dr = rr - rl;
    double dru = rum - rup;
    double drv = rvr - rvl;
    double drE = rEr - rEl;

    double rlam1 = fabs(uni+ci);
    double rlam2 = fabs(uni-ci);
    double rlam3 = fabs(uni);

    double s1 = 0.5 * (rlam1+rlam2);
    double s2 = 0.5 * (rlam1-rlam2);
    double al1x = gam1 * (af * dr - ui * dru - vi * drv + drE);
    double al2x = -uni * dr + dru * nx + drv * ny;
    double cc1 = ((s1-rlam3)* al1x / ci2) + s2 * al2x / ci;
    double cc2 = (s2 * al1x / ci) + (s1 - rlam3) * al2x;

    C[0] = -0.5 * (rlam3 * dr + cc1);
    C[1] = -0.5 * (rlam3 * dru + cc1* ui + cc2 * nx);
    C[2] = -0.5 * (rlam3 * drv + cc1 * vi + cc2 * ny);
    C[3] = -0.5 * (rlam3 * drE + cc1 * hi + cc2 * uni);
    
    return;
}
// end of file
