/**
 * @file two_diffusing_species.h
 * @brief Two diffusing species reaction-diffusion material model
 * <AUTHOR> Development Team
 * @date 2011-2023
 *
 * This file contains the TwoDiffusingSpecies class which implements a reaction-diffusion
 * material model for systems involving two interacting chemical species that undergo
 * both diffusion and chemical reactions. This model is fundamental for many applications
 * in chemistry, biology, materials science, and environmental engineering.
 */

#ifndef SUMMIT_TWO_DIFFUSING_SPECIES_H
#define SUMMIT_TWO_DIFFUSING_SPECIES_H

#include "../reaction_diffusion_material.h"

// Number of internal variables:
// the value is used to size the memory allocation for the material TwoDiffusingSpecies
                                         // 0 homogeneous reactions
#define TWO_DIFFUSING_SPECIES_NUMBER_INTERNAL_VARIABLES 1

namespace summit {

/**
 * @brief Two diffusing species reaction-diffusion material model
 *
 * The TwoDiffusingSpecies class implements a comprehensive reaction-diffusion material
 * model for systems involving two chemical species that undergo simultaneous diffusion
 * and chemical reactions. This model captures the complex interplay between mass
 * transport and chemical kinetics that governs many natural and engineered systems.
 *
 * ## Mathematical Formulation
 *
 * ### Governing Equations
 * The system is described by coupled reaction-diffusion equations:
 *
 * **Species A**: ∂C_A/∂t = D_A ∇²C_A + R_A(C_A, C_B)
 * **Species B**: ∂C_B/∂t = D_B ∇²C_B + R_B(C_A, C_B)
 *
 * Where:
 * - **C_A, C_B**: Concentrations of species A and B [mol/m³]
 * - **D_A, D_B**: Diffusion coefficients [m²/s]
 * - **R_A, R_B**: Reaction rate functions [mol/(m³·s)]
 * - **t**: Time [s]
 * - **∇²**: Laplacian operator [1/m²]
 *
 * ### Reaction Kinetics
 * Common reaction mechanisms include:
 *
 * #### Reversible Reaction
 * A ⇌ B with rates:
 * - R_A = -k_f C_A + k_r C_B
 * - R_B = +k_f C_A - k_r C_B
 *
 * #### Autocatalytic Reaction
 * A + B → 2B with rates:
 * - R_A = -k C_A C_B
 * - R_B = +k C_A C_B
 *
 * #### Competitive Inhibition
 * A + B → C with rates:
 * - R_A = -k C_A C_B / (1 + K_I C_B)
 * - R_B = -k C_A C_B / (1 + K_I C_B)
 *
 * #### Michaelis-Menten Kinetics
 * A → B (enzyme-catalyzed) with rates:
 * - R_A = -V_max C_A / (K_m + C_A)
 * - R_B = +V_max C_A / (K_m + C_A)
 *
 * ## Physical Applications
 *
 * ### Chemical Engineering
 * - **Reactor design**: Plug flow and CSTR reactors
 * - **Catalytic processes**: Heterogeneous and homogeneous catalysis
 * - **Separation processes**: Membrane separation and extraction
 * - **Polymerization**: Chain growth and step growth mechanisms
 *
 * ### Biological Systems
 * - **Enzyme kinetics**: Substrate-enzyme-product systems
 * - **Cell signaling**: Signal transduction pathways
 * - **Metabolic networks**: Glycolysis and TCA cycle
 * - **Pharmacokinetics**: Drug absorption, distribution, metabolism
 *
 * ### Materials Science
 * - **Corrosion processes**: Metal oxidation and passivation
 * - **Crystal growth**: Nucleation and growth kinetics
 * - **Phase transformations**: Solid-state reactions
 * - **Surface chemistry**: Adsorption and desorption processes
 *
 * ### Environmental Engineering
 * - **Groundwater contamination**: Contaminant transport and degradation
 * - **Atmospheric chemistry**: Ozone formation and depletion
 * - **Wastewater treatment**: Biological and chemical treatment
 * - **Soil chemistry**: Nutrient cycling and pollutant fate
 *
 * ### Pattern Formation
 * - **Turing patterns**: Reaction-diffusion instabilities
 * - **Spiral waves**: Excitable media dynamics
 * - **Front propagation**: Traveling wave solutions
 * - **Oscillatory behavior**: Temporal and spatial oscillations
 *
 * ## Key Features
 *
 * ### Diffusion Modeling
 * - **Fick's law**: Standard diffusion with constant coefficients
 * - **Concentration-dependent diffusion**: D = D(C_A, C_B)
 * - **Cross-diffusion**: Species A diffusion depends on species B gradient
 * - **Anisotropic diffusion**: Direction-dependent diffusion coefficients
 *
 * ### Reaction Modeling
 * - **Elementary reactions**: Mass action kinetics
 * - **Complex mechanisms**: Multi-step reaction pathways
 * - **Temperature dependence**: Arrhenius rate expressions
 * - **pH dependence**: Acid-base equilibria effects
 *
 * ### Coupling Effects
 * - **Diffusion-reaction competition**: Péclet and Damköhler numbers
 * - **Instability analysis**: Linear stability of uniform states
 * - **Bifurcation analysis**: Parameter-dependent behavior
 * - **Sensitivity analysis**: Parameter uncertainty quantification
 *
 * ## Numerical Implementation
 *
 * ### Spatial Discretization
 * - **Discontinuous Galerkin**: High-order accurate spatial discretization
 * - **Upwind stabilization**: Handles convection-dominated transport
 * - **Adaptive mesh refinement**: Resolves sharp concentration gradients
 * - **Interface handling**: Proper treatment of material interfaces
 *
 * ### Time Integration
 * - **Explicit methods**: Suitable for diffusion-dominated problems
 * - **Implicit methods**: Required for stiff reaction kinetics
 * - **IMEX schemes**: Implicit reactions, explicit diffusion
 * - **Adaptive time stepping**: Automatic time step control
 *
 * ### Nonlinear Solver
 * - **Newton-Raphson**: Quadratic convergence for smooth problems
 * - **Picard iteration**: Robust for highly nonlinear reactions
 * - **Continuation methods**: Parameter sweeping and bifurcation tracking
 * - **Preconditioning**: Efficient linear solver strategies
 *
 * ## Dimensionless Analysis
 *
 * ### Characteristic Scales
 * - **Length scale**: L (domain size or characteristic length)
 * - **Time scale**: τ (reaction time or diffusion time)
 * - **Concentration scale**: C₀ (initial or reference concentration)
 *
 * ### Dimensionless Parameters
 * - **Péclet number**: Pe = UL/D (convection vs. diffusion)
 * - **Damköhler number**: Da = kτ (reaction vs. transport)
 * - **Schmidt number**: Sc = ν/D (momentum vs. mass diffusion)
 * - **Lewis number**: Le = α/D (thermal vs. mass diffusion)
 *
 * ## Stability and Convergence
 *
 * ### Linear Stability Analysis
 * - **Uniform state stability**: Eigenvalue analysis
 * - **Turing instability**: Diffusion-driven instability
 * - **Hopf bifurcation**: Oscillatory instability
 * - **Pattern selection**: Wavelength selection mechanisms
 *
 * ### Numerical Stability
 * - **CFL condition**: Time step limitations for explicit schemes
 * - **Diffusion number**: Stability constraint for diffusion
 * - **Reaction stiffness**: Implicit treatment requirements
 * - **Conservation properties**: Mass conservation verification
 *
 * @note This model requires 1 internal variable for proper state tracking
 * @note Supports both 2D and 3D reaction-diffusion simulations
 * @note Can be extended to include convective transport terms
 * @note Integrates with Summit's adaptive mesh refinement capabilities
 *
 * @warning Stiff reaction kinetics may require small time steps or implicit methods
 * @warning Pattern formation problems need adequate spatial resolution
 * @warning Nonlinear reactions may exhibit multiple steady states
 * @warning Parameter sensitivity can be high near bifurcation points
 *
 * @see ReactionDiffusionMaterial for base class functionality
 * @see EulerEquations for fluid dynamics coupling
 * @see LinearConvectionDiffusion for simpler transport models
 */
class TwoDiffusingSpecies : public ReactionDiffusionMaterial {
  public:

    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    TwoDiffusingSpecies(const std::string& name);

    /**
     * Destructor
     */
    virtual ~TwoDiffusingSpecies();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    TwoDiffusingSpecies(const TwoDiffusingSpecies&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    TwoDiffusingSpecies& operator=(const TwoDiffusingSpecies&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * TwoDiffusingSpecies constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* concentration,
                              const real* u1,
                              const real* Dconcentration,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* concentration0, const real* concentration, real* q, real* dt, real* f, real* df, size_t ndf) const;


    /**
     * set the source term
     */
    void ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;

    /**
     * Compute the bulk modulus
     */
    real bulkModulus(real const* internal) const;
  
  protected: 

    /**
     * Source term
     */
    real _S;

    /**
     * diffusion
     */
    real _D1;

    /**
     * diffusion
     */
    real _D2;

    

  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
