#ifndef SUMMIT_GENERAL_PYROLYSIS_SOURCE_H
#define SUMMIT_GENERAL_PYROLYSIS_SOURCE_H

#include "../reaction_diffusion_material.h"
#include "../../mathlib/spline.h"
#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

// Number of internal variables:
// the value is used to size the memory allocation for the material GeneralPyrolysis
                                         // 0 homogeneous reactions
#define GENERAL_PYROLYSIS_NUMBER_INTERNAL_VARIABLES 55

namespace summit {

/**
 *
 */
class GeneralPyrolysis : public ReactionDiffusionMaterial {
  public:
    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    GeneralPyrolysis(const std::string& name);

    /**
     * Destructor
     */
    virtual ~GeneralPyrolysis();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    GeneralPyrolysis(const GeneralPyrolysis&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    GeneralPyrolysis& operator=(const GeneralPyrolysis&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    #ifdef WITH_YAML_CPP
    /**
     * Method to load properties from a YAML node
     * @param[in] yaml a YAML node containing properties for this material
     */
    virtual void Load(const YAML::Node& yamlNode) override;
    #endif

    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * GeneralPyrolysis constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* primal,
                              const real* u1,
                              const real* Dprimal,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* primal0, const real* primal, const real* Dprimal0, const real* Dprimal, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const;

    /**
     * set the source term
     */
    void ConvectiveFlux(const real* primal0, const real* primal, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;
  
      /**
     * Compute the bulk modulus
     */
    real bulkModulus(real const* internal) const;//this should be made virtual and reimplemented in future
    // also change to conductivity or something more sensible 

    /**
     * Compute the bulk modulus
     */
    void laxFriedrichStabilization(real const* primal0, real const* primal, real* C, real* dC) const;//this should be made virtual and reimplemented in future

    void calculateDecompositionRate(const real * primal, const real * primal0, real * q, real * dt) const;

    void calculatePartialHeatOfCharring(const real * primal, real * q) const;

    real calculateGasEnthalpy(const real T) const;

    real calculateEmissivity(const real density) const;

    real calculateSolidCharEnthalpy(const real T) const;

    real calculateDegreeOfChar(const real density) const;

    real calculateHeatFlux(const real T, const real beta, const real dT_dx) const;

    real calculateMassFlux(const real T, const real p, const real dp_dx) const;

  protected: 

    real _kChar;
    real _kVirgin;

    real _phiChar;
    real _phiVirgin;

    std::vector<real> _rhov;
    std::vector<real> _rhoc;
    std::vector<real> _B;
    std::vector<real> _E;
    std::vector<real> _psi;
    std::vector<real> _Treac;

    real _rho_C;
    real _Gamma;

    real _Rho_Total_Char;
    real _Rho_Total_Virgin;
    
    real _Tref;

    summit::spline _KappaCharInterpolant;
    summit::spline _KappaVirgInterpolant;

    summit::spline _CpCharInterpolant;
    summit::spline _CpVirgInterpolant;

    summit::spline _EnthalpiesCharInterpolant;
    summit::spline _EnthalpiesVirgInterpolant;
    

    summit::spline _GasEnthalpyInterpolant;
    summit::spline _GasViscosityInterpolant;
    summit::spline _GasMolarWeightInterpolant;
    summit::spline _GasCpInterpolant;
    

    int _NUM_OF_REACTIONS = 2;    

    // calculated in constitutive and source
    int _INT_TEMP = 0;

    // calculated in calculateDecompositionRate
    int _INT_DENSITY_SOLID = 1;

    // calculated in constitutive and source
    int _INT_DENSITY_GAS = 2;
    int _INT_DENSITY_BULK = 3;

    // calculated in calculateDecompositionRate
    int _INT_DDENSITY_SOLID_DT = 4;
    int _INT_D2DENSITY_SOLID_DT2 = 5;

    // calculated in constitutive and source
    int _INT_DEGREE_OF_CHAR = 6;
    int _INT_YC = 7;
    int _INT_YV = 8;
    int _INT_YS = 9;
    int _INT_YG = 10;

    // calculated in calculatePartialHeatOfCharring
    int _INT_H_CHAR = 11;
    int _INT_H_VIRGIN = 12;
    int _INT_PARTIAL_HEAT_CHAR = 13;
    int _INT_DPARTIAL_HEAT_CHAR_DT = 14;

    // calculated in constitutive and source
    int _INT_GAS_VISCOSITY = 15;
    int _INT_GAS_MOLAR_WEIGHT = 16;
    int _INT_GAS_ENTHALPY = 17;

    // calculated in constitutive
    int _INT_KAPPA_CHAR = 18;
    int _INT_KAPPA_VIRGIN = 19;
    int _INT_KAPPA = 20;

    // calculated in source
    int _INT_CP_CHAR = 21;
    int _INT_CP_VIRGIN = 22;
    int _INT_CP_SOLID = 23;
    int _INT_CP_GAS = 24;
    int _INT_CP = 25;

    // calculated in constitutive and source
    int _INT_PERMEABILITY = 26;
    int _INT_POROSITY = 27;

    // calculate in constitutive
    int _INT_DIFFUSIVE_THERMAL_FLUX = 28;
    int _INT_ADVECTIVE_THERMAL_FLUX = 31;
    int _INT_DIFFUSIVE_MASS_FLUX = 34;
    int _INT_GAS_VELOCITY = 37;

    // saved in calculateDecompositionRate
    int _INT_DECOMP_RATE = 40;
    int _INT_DDECOMP_RATE_DT = 41;
    int _INT_D2DECOMP_RATE_DT2 = 42;
    int _INT_COMPONENT_SOLID_DENS = 43;
    int _INT_COMPONENT_DECOMP_RATE = 45;
    int _INT_COMPONENT_DDECOMP_RATE_DT = 47;
    int _INT_COMPONENT_D2DECOMP_RATE_DT2 = 49;

    int _INT_TIME = 51;
    int _INT_COORDINATES = 52;

  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
