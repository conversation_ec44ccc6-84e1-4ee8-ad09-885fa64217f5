#ifndef SUMMIT_TORRES_HERRADOR_SOURCE_H
#define SUMMIT_TORRES_HERRADOR_SOURCE_H

#include "../reaction_diffusion_material.h"
#include "../../mathlib/spline.h"

// Number of internal variables:
// the value is used to size the memory allocation for the material TorresHerrador
                                         // 0 homogeneous reactions
#define TORRES_HERRADOR_NUMBER_INTERNAL_VARIABLES 64

namespace summit {

/**
 *
 */
class TorresHerrador : public ReactionDiffusionMaterial {
  public:
    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    TorresHerrador(const std::string& name);

    /**
     * Destructor
     */
    virtual ~TorresHerrador();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    TorresHerrador(const TorresHerrador&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    TorresHerrador& operator=(const TorresHerrador&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * TorresHerrador constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* primal,
                              const real* u1,
                              const real* Dprimal,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* primal0, const real* primal, const real* Dprimal0, const real* Dprimal, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const;

    /**
     * set the source term
     */
    void ConvectiveFlux(const real* primal0, const real* primal, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;
  
      /**
     * Compute the bulk modulus
     */
    real bulkModulus(real const* internal) const;//this should be made virtual and reimplemented in future
    // also change to conductivity or something more sensible 

    /**
     * Compute the bulk modulus
     */
    void laxFriedrichStabilization(real const* primal0, real const* primal, real* C, real* dC) const;//this should be made virtual and reimplemented in future

    void calculateDecompositionRate(const real * primal, real * q, real * dt) const;

    void calculateTau(const real * primal, real * q) const;

    void calculatePartialHeatOfCharring(const real * primal, real * q) const;

    void calculateGasEnthalpy(const real * primal, real * q) const;

    void GetThermalConductivity(const real * primal, real * q) const;

    void GetCp(const real * primal, real * q) const;

    void calculateGasMassFlux(const real * primal, const real * Dprimal, real * q) const;

    void InitializeGasProperties();

  protected: 

    /**
     * final mass fraction
     */
    real _rho;

    /**
     * solid thermal conductivity
     */
    real _heatCapacity;

    /**
     * solid thermal conductivity
     */
    real _thermalConductivity;

    /**
     * final mass fraction
     */
    real _Permeability;

    /**
     * enthalpy of gas phase
     */
    real _mu;

    // std::vector<real> _rhov;
    // std::vector<real> _rhoc;
    // std::vector<real> _B;
    // std::vector<real> _E;
    // std::vector<real> _psi;
    // std::vector<real> _Treac;

    real _kChar;
    real _kVirgin;

    real _phiChar;
    real _phiVirgin;

    real _rho_C;
    real _Gamma;

    real _Rho_Total_Char = 220.0;
    real _Rho_Total_Virgin = 280.0;

    real _phi = 0.8;

    std::vector<real> _Temperatures;
    std::vector<real> _CapacitiesVirgin;
    std::vector<real> _ThermalConductivitiesVirgin;
    std::vector<real> _EnthalpiesVirgin;
    std::vector<real> _CapacitiesChar;
    std::vector<real> _ThermalConductivitiesChar;
    std::vector<real> _EnthalpiesChar;

    summit::spline _KappaCharInterpolant;
    summit::spline _KappaVirgInterpolant;

    summit::spline _CpCharInterpolant;
    summit::spline _CpVirgInterpolant;

    summit::spline _EnthalpiesCharInterpolant;
    summit::spline _EnthalpiesVirgInterpolant;

    std::vector<real> _PressurePyrolysisGas;
    std::vector<real> _TemperaturesPyrolysisGas;
    std::vector<real> _MPyrolysisGas;
    std::vector<real> _CpPyrolysisGas;
    std::vector<real> _GammaPyrolysisGas;
    std::vector<real> _hPyrolysisGas;
    std::vector<real> _MuPyrolysisGas;
    std::vector<real> _RhoPyrolysisGas;

    summit::spline _GasEnthalpyInterpolant;
    summit::spline _GasViscosityInterpolant;
    summit::spline _GasMolarWeightInterpolant;

    std::vector<real> _UniquePressurePyrolysisGas;
    std::vector<real> _UniqueTemperaturesPyrolysisGas;

    int _NUM_OF_REACTIONS = 6;
    int _NUM_OF_GAS_PRODUCTS = 17;

    int _INT_TEMP = 0;

    int _INT_EXTENT_OF_REACTION_RATE = 1;
    int _INT_EXTENT_OF_REACTION = 7;
    int _INT_EXTENT_OF_REACTION_RATE_DT = 13;

    int _INT_DECOMP_RATE = 19;
    int _INT_DDECOMP_RATE_DT = 20;

    int _INT_SOLID_DENS = 21;
    int _INT_DSOLID_DENS_DT = 22;
    
    int _INT_GAS_ENTHALPY = 23;
    int _INT_H_CHAR = 24;
    int _INT_H_VIRGIN = 25;
    int _INT_PARTIAL_HEAT_CHAR = 26;
    int _INT_DGAS_ENTHALPY_DT = 27;
    int _INT_DPARTIAL_HEAT_CHAR_DT = 28;

    int _INT_KAPPA_CHAR = 29;
    int _INT_KAPPA_VIRGIN = 30;
    int _INT_KAPPA = 31;
    int _INT_DKAPPA_DT = 32;

    int _INT_CP_CHAR = 33;
    int _INT_CP_VIRGIN = 34;
    int _INT_CP = 35;
    int _INT_DCP_DT = 36;

    int _INT_DEGREE_OF_CHAR = 37;
    int _INT_TAU = 38;
    int _INT_DTAU_DT = 39;

    int _INT_COMPONENT_GAS_PRODUCTION_RATE = 40;

    int _INT_MDOT = 57;

    int _INT_GAS_VELOCITY = 60;

    int _INT_GAS_DENSITY = 63;
    
    std::vector<real> _molarMass;
    std::vector<real> _rates;
    std::vector<real> _activationEnergy;
    std::vector<real> _exponent;
    std::vector<real> _resinFraction;
    std::vector<real> _gasFraction;
    std::vector<std::vector<real>> _stoichiometricCoefficients;


  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
