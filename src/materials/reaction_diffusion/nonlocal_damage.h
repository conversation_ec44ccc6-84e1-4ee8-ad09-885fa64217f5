/**
 * @file nonlocal_damage.h
 * <AUTHOR> pickard (<EMAIL>)
 * @brief A material model for nonlocal damage mechanics
 * 
 * This file implements a nonlocal damage model that regularizes the damage field
 * through a reaction-diffusion approach, avoiding mesh-dependent solutions and
 * ill-posedness that occur in local damage formulations.
 */

#ifndef SUMMIT_NONLOCAL_DAMAGE_H
#define SUMMIT_NONLOCAL_DAMAGE_H

#include "../reaction_diffusion_material.h"

// Number of internal variables:
// the value is used to size the memory allocation for the material NonlocalDamage
                                         // 0 homogeneous reactions
#define NONLOCAL_DAMAGE_NUMBER_INTERNAL_VARIABLES 3

namespace summit {

/**
 * @brief Implementation of a nonlocal damage model using a reaction-diffusion approach
 * 
 * @details This class implements a nonlocal damage model that regularizes the damage field
 * through a reaction-diffusion equation. Nonlocal damage models address the mesh-dependency
 * and ill-posedness issues that arise in local damage formulations by introducing a
 * characteristic length scale.
 * 
 * The model is based on the following governing equation:
 * 
 * \f[ \frac{\partial d}{\partial t} - c \nabla^2 d = f(d, \varepsilon) \f]
 * 
 * where \f$d\f$ is the damage variable, \f$c\f$ is a diffusion coefficient related to
 * the material's characteristic length, and \f$f(d, \varepsilon)\f$ is a source term
 * that depends on the current damage state and strain.
 * 
 * Key features of this model include:
 * - Mesh-independent solutions
 * - Well-posed boundary value problems
 * - Physically meaningful damage localization
 * - Regularization of strain softening behavior
 */
class NonlocalDamage : public ReactionDiffusionMaterial {
  public:
    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    NonlocalDamage(const std::string& name);

    /**
     * Destructor
     */
    virtual ~NonlocalDamage();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    NonlocalDamage(const NonlocalDamage&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    NonlocalDamage& operator=(const NonlocalDamage&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * NonlocalDamage constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* concentration,
                              const real* u1,
                              const real* Dconcentration,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const;

    /**
     * set the source term
     */
    void ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;
  
  protected: 

    /**
     * energy of cavitation
     */
    real _G;

    /**
     * length scale
     */
    real _Epsilon;

    /**
     * analogue to density, the "dynamic modulus"
     */
    real _Zeta;
    
    /**
     * damage threshold
     */
    real _offset;
    
  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
