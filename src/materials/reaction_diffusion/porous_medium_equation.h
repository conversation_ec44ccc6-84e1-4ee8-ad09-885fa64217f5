/**
 * @file porous_medium_equation.h
 * <AUTHOR> pickard (<EMAIL>)
 * @brief A material model for nonlinear diffusion in porous media
 * 
 * This file implements the porous medium equation, a nonlinear diffusion model
 * that describes processes like gas flow through porous media, heat conduction
 * with temperature-dependent conductivity, and biological population spreading.
 */

#ifndef SUMMIT_POROUS_MEDIUM_EQUATION_H
#define SUMMIT_POROUS_MEDIUM_EQUATION_H

#include "../reaction_diffusion_material.h"

// Number of internal variables:
// the value is used to size the memory allocation for the material PorousMediumEquation
                                         // 0 homogeneous reactions
#define POROUS_MEDIUM_EQUATION_NUMBER_INTERNAL_VARIABLES 5

namespace summit {

/**
 * @brief Implementation of the porous medium equation for nonlinear diffusion
 * 
 * @details The porous medium equation is a nonlinear partial differential equation
 * of the form:
 * 
 * \f[ \frac{\partial u}{\partial t} = \nabla \cdot (D u^m \nabla u) \f]
 * 
 * where \f$u\f$ is the concentration or density, \f$D\f$ is the diffusion coefficient,
 * and \f$m\f$ is the nonlinearity exponent. This equation models:
 * 
 * - Flow of gases through porous media
 * - Nonlinear heat conduction
 * - Groundwater infiltration
 * - Population dynamics
 * 
 * The nonlinearity creates interesting phenomena like finite speed of propagation
 * and self-similar solutions. This implementation supports both the standard form
 * and variants with additional source/sink terms.
 */
class PorousMediumEquation : public ReactionDiffusionMaterial {
  public:

    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    PorousMediumEquation(const std::string& name);

    /**
     * Destructor
     */
    virtual ~PorousMediumEquation();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    PorousMediumEquation(const PorousMediumEquation&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    PorousMediumEquation& operator=(const PorousMediumEquation&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * PorousMediumEquation constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* concentration,
                              const real* u1,
                              const real* Dconcentration,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* concentration0, const real* concentration, real* q, real* dt, real* f, real* df, size_t ndf) const;


    /**
     * set the source term
     */
    void ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;

    /**
     * Compute the bulk modulus
     */
    real bulkModulus(real const* internal) const;//this should be made virtual and reimplemented in future
    // also change to conductivity or something more sensible 
  

  protected: 
    /**
     * Method to perform change of variables for nonlinear heat transfer
     */
    real _T_from_phi(real phi) const;

    /**
     * Method to perform change of variables for nonlinear heat transfer
     */
    real _phi_from_T(real T) const;
    
    /**
     * Nonlinear conductivity
     */
    real _conductivity(real T) const;

    /**
     * Nonlinear conductivity
     */
    real _dconductivity(real T) const;

    /**
     * final mass fraction
     */
    real _rho;

    /**
     * coefficient
     */
    real _m;

  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();

};
}  // namespace summit

#endif
