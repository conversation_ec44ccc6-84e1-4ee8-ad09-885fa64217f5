/**
 * @file variational_conduction.h
 * <AUTHOR> pickard (<EMAIL>)
 * @brief A material model for heat conduction based on variational principles
 * 
 * This file implements a material model for heat conduction derived from
 * variational principles, which provides a consistent framework for both
 * the governing equations and boundary conditions.
 */

#ifndef SUMMIT_VARIATIONAL_CONDUCTION_H
#define SUMMIT_VARIATIONAL_CONDUCTION_H

#include "../reaction_diffusion_material.h"

// Number of internal variables:
// the value is used to size the memory allocation for the material VariationalConduction
                                         // 0 homogeneous reactions
#define VARIATIONAL_CONDUCTION_NUMBER_INTERNAL_VARIABLES 5

namespace summit {

/**
 * @brief Implementation of a variational approach to heat conduction
 * 
 * @details This class implements a heat conduction model derived from variational
 * principles. Unlike traditional formulations that directly discretize the heat
 * equation, this approach starts from an energy functional whose minimization
 * yields the governing equations and natural boundary conditions.
 * 
 * The model is based on the functional:
 * 
 * \f[ J(T) = \int_\Omega \left( \frac{1}{2} k |\nabla T|^2 - QT \right) d\Omega - \int_{\Gamma_q} qT d\Gamma \f]
 * 
 * where \f$T\f$ is temperature, \f$k\f$ is thermal conductivity, \f$Q\f$ is a heat source,
 * and \f$q\f$ is a prescribed heat flux on boundary \f$\Gamma_q\f$.
 * 
 * Advantages of this approach include:
 * - Naturally symmetric system matrices
 * - Consistent treatment of boundary conditions
 * - Improved numerical stability
 * - Straightforward extension to nonlinear problems
 * 
 * This implementation supports both linear and nonlinear thermal conductivity,
 * steady-state and transient problems, and various boundary conditions.
 */
class VariationalConduction : public ReactionDiffusionMaterial {
  public:

    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    VariationalConduction(const std::string& name);

    /**
     * Destructor
     */
    virtual ~VariationalConduction();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    VariationalConduction(const VariationalConduction&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    VariationalConduction& operator=(const VariationalConduction&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * VariationalConduction constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* concentration,
                              const real* u1,
                              const real* Dconcentration,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const;


    /**
     * set the source term
     */
    void ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;

    /**
     * Compute the bulk modulus
     */
    real bulkModulus(real const* internal) const;//this should be made virtual and reimplemented in future
    // also change to conductivity or something more sensible 
  

  protected: 
    /**
     * Method to perform change of variables for nonlinear heat transfer
     */
    real _T_from_phi(real phi) const;

    /**
     * Method to perform change of variables for nonlinear heat transfer
     */
    real _phi_from_T(real T) const;
    
    /**
     * Nonlinear conductivity
     */
    real _conductivity(real T) const;

    /**
     * Nonlinear conductivity
     */
    real _dconductivity(real T) const;
    
    /**
     * final mass fraction
     */
    real _rho;

    /**
     * solid Cv
     */
    real _Cv;

    /**
     * first term
     */
    real _A;

    /**
     * second term
     */
    real _B;

    /**
     * third term
     */
    real _C;

    /**
     * fourth term
     */
    real _D;

    /**
     * coefficient of thermal expansion
     */
    real _cte;

    /**
     * bulk modulus
     */
    real _bulk;

    /**
     * reference temperature
     */
    real _Tref;

  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();

};
}  // namespace summit

#endif
