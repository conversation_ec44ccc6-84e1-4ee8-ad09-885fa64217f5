#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>

#include "nonlocal_damage.h"
#include "../../mathlib/mathlib.h"
#include "../../io/summit_message.h"

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    0
// ****************************************************

summit::NonlocalDamage::NonlocalDamage(const std::string& name)
  : ReactionDiffusionMaterial(name, 3)
{
    // fill the map
    _setInternalVariableMap();
}

summit::NonlocalDamage::~NonlocalDamage() {
    // fill the map
    _setInternalVariableMap();
}

void summit::NonlocalDamage::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);

    _G = values[0];

    _Epsilon = values[1];

    _Zeta = values[2];

    _offset = values[3];
    

    // all done
    return;
}

void summit::NonlocalDamage::Display()
{
    // Check material properties
    Message::Info("Nonlocal Damage Model for Elastomeric Materials:");
    Message::Info("\tEnergy of Cavitation....... = %e", _G);
    Message::Info("\tLength scale....... = %e", _Epsilon);
    Message::Info("\tTime scale....... = %e", _Zeta);
    Message::Info("\tThreshold....... = %e", _offset);
    
    // end of method
    return;
}

summit::real summit::NonlocalDamage::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    // diffusivity
    //return _thermal_conductivity / (_rho * _heat_capacity);
    return _Epsilon * _G / (_Zeta);
}

void summit::NonlocalDamage::Constitutive(const real* concentration,
                                                           const real* u1,
                                                           const real* Dconcentration,
                                                           const real* Du1,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dtime,
                                                           const int ndf, // number of components in concentration
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{
    int strain_dim = ndm * ndf;

    // for (int dimension = 0; dimension < ndm; dimension++){
    //     P[dimension + 0 * ndm] = _eps *_eps * (Dconcentration[dimension + 0 * ndm] / 2 + Du1[dimension + 0 * ndm] / 2);
    //     tangent[(dimension + 0 * ndm) * strain_dim + dimension + 0 * ndm] =  _eps *_eps / 2;
    // }
    for (int dimension = 0; dimension < ndm; dimension++){
        P[dimension + 0 * ndm] = _Epsilon *_G * Dconcentration[dimension + 0 * ndm];
        if(compute_tangents){
            tangent[(dimension + 0 * ndm) * strain_dim + dimension + 0 * ndm] =   _Epsilon *_G;
        }
    }
    return;
}


summit::real summit::NonlocalDamage::capacity(real const* internal, const int component) const
{
    //analogue to density
    //dynamic modulus in Anand's terminology
    //maybe it was kinetic modulus?
    return _Zeta;    
}

void summit::NonlocalDamage::_setInternalVariableMap()
{   
    // all done
    std::cout << "I AM INSIDE THE CORRECT TABLE LOOP!" <<std::endl;
    SetLocationInInternalTable("damage_from_nonlocal", 0, 1);
    SetLocationInInternalTable("strain_energy_to_nonlocal", 1, 1);
    SetLocationInInternalTable("history", 2, 1);
    return;
}

void summit::NonlocalDamage::Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const
{
    real d = concentration[0];
    if((q[1] - _offset) > q[2]){
        q[2] = (q[1] - _offset);
    }
    f[0] = 2 * (1 - d) * q[2] - _G / _Epsilon * d;
    df[0] = -2 * q[2] - _G / _Epsilon;
    q[0] = d;
    
    

    // real d = concentration[0] + concentration0[0] ) / 2.0;
    // //do not let damage go over ones
    // if(concentration[0] > 1){
    //     d = (1 + concentration0[0] ) / 2.0;
    // }
    // f[0] = (1 - d) * q[2] - _Epsilon / 2.0 * d;
    // df[0] = - q[2] / 2.0 - _Epsilon / 4.0;


    // // real d = concentration[0];
    // // //do not let damage go over ones
    // // f[0] = (1 - d) * q[2] - _Epsilon / 2.0 * d;
    // // df[0] = - q[2] - _Epsilon / 2.0;
    // if(d > 0){
    //     if(d < 1){
    //         if(d > q[0]){
    //             q[0] = d;
    //         }
    //         //q[0] = d;
    //     }
    // }
    
    return;
}

void summit::NonlocalDamage::ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{
    return;
}

int summit::NonlocalDamage::number_unknowns() const
{
    return 1;
}

// end of file
