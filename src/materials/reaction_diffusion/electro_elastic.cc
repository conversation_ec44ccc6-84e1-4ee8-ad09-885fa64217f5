#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>

#include "electro_elastic.h"
#include "../../mathlib/mathlib.h"
#include "../../io/summit_message.h"

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    0
// ****************************************************

summit::ElectroElastic::ElectroElastic(const std::string& name)
  : ReactionDiffusionMaterial(name, ELECTRO_ELASTIC_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    _setInternalVariableMap();
}

summit::ElectroElastic::~ElectroElastic() {}

void summit::ElectroElastic::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);

    _rho = values[0];

    real E = values[1];
    
    real nu = values[2];

    _lambda = nu * E / (1 + nu) / (1 - 2 * nu);
    _mu = E / 2 / (1 + nu);

    _C1 = values[3];

    _C2 = values[4];

    // all done
    return;
}

void summit::ElectroElastic::Display()
{
    // Check material properties
    Message::Info("Electro Elastic Model:");
    Message::Info("\tC1....... = %e", _C1);
    Message::Info("\tC2....... = %e", _C2);    
    Message::Info("\tMu....... = %e", _mu);
    Message::Info("\tLambda....... = %e", _lambda);    
    
    // end of method
    return;
}

summit::real summit::ElectroElastic::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    // diffusivity
    //return _thermal_conductivity / (_rho * _heat_capacity);
    //this is important for stabilizing the DG elements (I think)
    return 0.0;
}

void summit::ElectroElastic::Constitutive(const real* concentration0,
                                                           const real* concentration,
                                                           const real* Dconcentration0,
                                                           const real* Dconcentration,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dtime,
                                                           const int ndf, // number of components in concentration
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{
    real C[9] = { 1., 0., 0., 0., 1., 0., 0., 0., 1. };
    real CInv[9] = { 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0 };
    real JC,trC, nE, eCe;
    real E[3] = { 0., 0., 0.};
    
    for (int i = 0; i < 9; i++){
        // access right Cauchy Green Tensor
        C[i] += q[i];
    }

    //Potentially move up in dimension
    for (int i = 0; i < ndm; i++){
        E[i] = Dconcentration[i];
    }

    for (int i = 0; i < 3; i++){
        // load the electric field into internal table - always assume transfer is of the 3D field
        // Change the sign here because Dconcentration is -E, but we want to pass 
        // the physical (i.e. correct sign) E field to the mechanics system.
        q[i+9] = -1.0 * E[i];
    }

    real F1Inv[9] = { 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0 };
    JC = MathMat3Inv(C, CInv);
    JC = sqrt(JC);
    trC = C[0] + C[4] + C[8];
    nE = sqrt(E[0] * E[0] + E[1] * E[1] + E[2] * E[2]);
    eCe = 0;
    for (int i = 0; i < 3; i++){
        for (int j = 0; j < 3; j++){
            eCe += E[i] * C[i * 3 + j] * E[j];
        }
    }
    real FreeEnergy = _mu / 2.0 * (trC - 3.0) - _mu * log(JC) + _lambda / 2.0 * log(JC) * log(JC) + _C1 * nE + _C2 * eCe;

    // From Vu et. al: -D = 2 * c1 * E + 2 * c2 * C*E
    // We want -D because that is what the reaction-diffusion weak form expects
    for (int i = 0; i < 3; i++){
        // add first C1 term to electric displacement
        P[i] = 2.0 * _C1 * E[i];
        tangent[i * ndm + i] = 2.0 * _C1;
    }
    for (int i = 0; i < 3; i++){
        // add second C2 term to electric displacement
        for (int j = 0; j < 3; j++){
            P[i] += 2.0 * _C2 * C[i * 3 + j] * E[j];
            tangent[i * ndm + j] += 2.0 * _C2 * C[i * 3 + j];
        }
    }
    return;
}


summit::real summit::ElectroElastic::capacity(real const* internal, const int component) const
{
    return 0.0;//_heatCapacity * _rho;    
}

void summit::ElectroElastic::_setInternalVariableMap()
{   
    // all done
    SetLocationInInternalTable("Right Cauchy Green", 0, 9);
    SetLocationInInternalTable("Electric Field", 9, 3);
    return;
}

void summit::ElectroElastic::Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const
{   
    //f[0] = 1.0 * dt[0];
    f[0] = 0.0; //_rho; add source terms here! tbd
    df[0] = 0.0;
    return;
}

void summit::ElectroElastic::ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{
    //F[0] = 1.0e2 * concentration[0];
    return;
}

int summit::ElectroElastic::number_unknowns() const
{
    return 1;
}

real summit::ElectroElastic::bulkModulus(real const* internal) const
{
    return _C1;
}
// end of file
