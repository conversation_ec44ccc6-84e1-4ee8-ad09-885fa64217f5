#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>

#include "variational_conduction.h"
#include "../../mathlib/mathlib.h"
#include "../../io/summit_message.h"

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    0
// ****************************************************

summit::VariationalConduction::VariationalConduction(const std::string& name)
  : ReactionDiffusionMaterial(name, 5)
{
    // fill the map
    _setInternalVariableMap();
}

summit::VariationalConduction::~VariationalConduction() {}

void summit::VariationalConduction::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);

    _rho = values[0];

    _Cv = values[1];

    _A = values[2];

    _B = values[3];

    _C = values[4];

    _D = values[5];

    _cte = values[6];
    
    _bulk = values[7];

    _Tref = values[8];

    // all done
    return;
}

void summit::VariationalConduction::Display()
{
    // Check material properties
    Message::Info("\tDensity....... = %e", _rho);
    Message::Info("\tHeat Capacity....... = %e", _Cv);
    Message::Info("\tSeries Term A....... = %e", _A);
    Message::Info("\tSeries Term B....... = %e", _B);
    Message::Info("\tSeries Term C....... = %e", _C);
    Message::Info("\tSeries Term D....... = %e", _D);
    Message::Info("\tCoefficient of Thermal Expansion....... = %e", _cte);
    Message::Info("\tBulk Modulus....... = %e", _bulk);
    Message::Info("\tReference Temperature....... = %e", _Tref);
    // end of method
    return;
}

summit::real summit::VariationalConduction::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    // diffusivity
    //return _thermal_conductivity / (_rho * _heat_capacity);
    //this is important for stabilizing the DG elements (I think)
    return _D / (_rho * _Cv);
}

summit::real summit::VariationalConduction::_T_from_phi(const real phi) const
{
    // diffusivity
    //return _thermal_conductivity / (_rho * _heat_capacity);
    //this is important for stabilizing the DG elements (I think)
    real T = phi / _D;
    real error = this->_phi_from_T(T) - phi;
    //std::cout << "error is: " << error << std::endl;
    while (std::abs(error)>std::abs(phi)*1e-12){
        T -= error / this->_conductivity(T);
        error = this->_phi_from_T(T) - phi;
        //std::cout << "error is: " << error << std::endl;
    }
    return T;
}

summit::real summit::VariationalConduction::_phi_from_T(const real T) const
{   
    //integral of the conductivity
    return (_A * std::pow(T,4.0)/4.0 + _B * std::pow(T,3.0)/3.0 + _C * std::pow(T,2.0)/2.0 + _D * std::pow(T,1.0)/1.0);
}

summit::real summit::VariationalConduction::_conductivity(const real T) const
{   
    //derivative of the _phi_from_T function
    return (_A * std::pow(T,3.0) + _B * std::pow(T,2.0) + _C * std::pow(T,1.0) + _D);
}
summit::real summit::VariationalConduction::_dconductivity(const real T) const
{   
    //derivative of the _phi_from_T function
    return (_A * std::pow(T,2.0) * 3.0 + _B * T * 2.0 + _C);
}


void summit::VariationalConduction::Constitutive(const real* concentration0,
                                                           const real* concentration,
                                                           const real* Dconcentration0,
                                                           const real* Dconcentration,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dtime,
                                                           const int ndf, // number of components in concentration
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{
    
    for (int component = 0; component < this->number_unknowns(); component++){
        for (int dimension = 0; dimension < ndm; dimension++){
            P[dimension + component * ndm] = Dconcentration[dimension + component * ndm];
            if(compute_tangents){
                tangent[(dimension + component * ndm) * (ndm * ndf) + dimension + component * ndm] = 1.0;
            }
        }
    }
    real T = this->_T_from_phi(concentration[0]);
    q[0] = T;
    return;
}


summit::real summit::VariationalConduction::capacity(real const* internal, const int component) const
{
    return 0.0;//_heatCapacity * _rho;    
}

void summit::VariationalConduction::_setInternalVariableMap()
{   
    // all done
    SetLocationInInternalTable("Temperature", 0, 1);
    SetLocationInInternalTable("Jacobian", 1, 1);
    SetLocationInInternalTable("Jacobian Old", 2, 1);
    SetLocationInInternalTable("plastic work", 3, 1);
    SetLocationInInternalTable("plastic work old", 4, 1);
    return;
}

void summit::VariationalConduction::Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const
{   
    real phi = concentration[0];
    real phi0 = concentration0[0];
    real T = this->_T_from_phi(phi);
    real T0 = this->_T_from_phi(phi0);
    real K =this->_conductivity(T);
    real dK = this->_dconductivity(T);
    // nonlinear heat capacity
    f[0] = - (_rho*_Cv - T *_cte*_cte*_bulk) / dt[0] * (phi - phi0) / K;
    //derivative of dphidt
    df[0] = - (_rho*_Cv - T *_cte*_cte*_bulk) / dt[0] / K;
    //derivative of 1/K
    df[0] += - (_rho*_Cv - T *_cte*_cte*_bulk) / dt[0] * (phi - phi0) / (-K*K*K) * dK;
    // derivative of consistent heat capacity
    df[0] += (_cte*_cte*_bulk / K) / dt[0] * (phi - phi0) / K;
    // add plastic heating rate
    f[0] += q[3] - q[4];
    // add thermoelastic effect
    f[0] += (T + _Tref) * (-_bulk * _cte) * (q[1] - q[2]) / dt[0];
    df[0] += 1/K * (-_bulk * _cte) * (q[1] - q[2]) / dt[0];  
    // update Jacobians and plastic work
    q[2] = q[1];
    q[4] = q[3];
    return ;
}

void summit::VariationalConduction::ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{
    return;
}

int summit::VariationalConduction::number_unknowns() const
{
    return 1;
}

real summit::VariationalConduction::bulkModulus(real const* internal) const
{
    return 1.0;
}
// end of file
