#include <pyre/journal.h>
#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>

#include "General_Pyrolysis.h"
#include "../../mathlib/mathlib.h"
#include "../../io/summit_message.h"

#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    0
// ****************************************************

summit::GeneralPyrolysis::GeneralPyrolysis(const std::string& name)
  : ReactionDiffusionMaterial(name, GENERAL_PYROLYSIS_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    _setInternalVariableMap();
}

summit::GeneralPyrolysis::~GeneralPyrolysis() {}

void summit::GeneralPyrolysis::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);

    _rhov.resize(2,0);
    _rhoc.resize(2,0);
    _B.resize(2,0);
    _E.resize(2,0);
    _psi.resize(2,0);
    _Treac.resize(2,0);

    _rhov = {900.0, 300.0};
    _rhoc = {600.0, 0.0};
    _psi = {3.0, 3.0};
    _E = {20444.0, 8556.0};
    _B = {4.48e9, 1.20e4};
    _Treac = {555.6,333.3};
    _rho_C = 1600.0;
    _Gamma = 0.5;

    _kChar = 2.0e-11;
    _kVirgin = 1.6e-11;

    _phiChar = 0.85;
    _phiVirgin = 0.8;

    // all done
    return;
}

#ifdef WITH_YAML_CPP
void summit::GeneralPyrolysis::Load(const YAML::Node &yamlNode) {

    pyre::journal::error_t error("summit.materials.reaction_diffusion.GeneralPyrolysis");
    
    // Read material parameters
    try {
        _Rho_Total_Char = yamlNode["char density"].as<real>();
        _Rho_Total_Virgin = yamlNode["virgin density"].as<real>();
        _Tref = yamlNode["reference temperature"].as<real>();
        _Gamma = yamlNode["volume fractions"].as<real>();

        _rhov = yamlNode["component virgin density"].as<std::vector<real>>();
        _rhoc = yamlNode["component char density"].as<std::vector<real>>();
        _B = yamlNode["arhenius coefficient"].as<std::vector<real>>();
        _E = yamlNode["activation energy"].as<std::vector<real>>();
        _psi = yamlNode["reaction order"].as<std::vector<real>>();
        _Treac = yamlNode["activation temperature"].as<std::vector<real>>();

        _kChar = yamlNode["char permeability"].as<real>();
        _kVirgin = yamlNode["virgin permeability"].as<real>();

        _phiChar = yamlNode["char porosity"].as<real>();
        _phiVirgin = yamlNode["virgin porosity"].as<real>();

        _rho_C = yamlNode["virgin fiber density"].as<real>();

        std::vector<real> Temperatures = yamlNode["lookup solid temperature"].as<std::vector<real>>();
        std::vector<real> CapacitiesVirgin = yamlNode["lookup virgin specific heat capacity"].as<std::vector<real>>();
        std::vector<real> CapacitiesChar = yamlNode["lookup char specific heat capacity"].as<std::vector<real>>();
        std::vector<real> ThermalConductivitiesVirgin = yamlNode["lookup virgin thermal conductivity"].as<std::vector<real>>();
        std::vector<real> ThermalConductivitiesChar = yamlNode["lookup char thermal conductivity"].as<std::vector<real>>();
        std::vector<real> EnthalpiesVirgin = yamlNode["lookup solid virgin enthalpies"].as<std::vector<real>>();
        std::vector<real> EnthalpiesChar = yamlNode["lookup solid char enthalpies"].as<std::vector<real>>();


        _KappaCharInterpolant.set_points(Temperatures, ThermalConductivitiesChar);
        _KappaVirgInterpolant.set_points(Temperatures, ThermalConductivitiesVirgin);

        _CpCharInterpolant.set_points(Temperatures, CapacitiesChar);
        _CpVirgInterpolant.set_points(Temperatures, CapacitiesVirgin);

        _EnthalpiesCharInterpolant.set_points(Temperatures, EnthalpiesChar);
        _EnthalpiesVirgInterpolant.set_points(Temperatures, EnthalpiesVirgin);

        std::vector<real> TemperaturesPyrolysisGas = yamlNode["lookup gas temperature"].as<std::vector<real>>();
        std::vector<real> hPyrolysisGas = yamlNode["lookup gas enthalpy"].as<std::vector<real>>();
        std::vector<real> MuPyrolysisGas = yamlNode["lookup gas viscosity"].as<std::vector<real>>();
        std::vector<real> MPyrolysisGas = yamlNode["lookup gas molar mass"].as<std::vector<real>>();
        std::vector<real> CpPyrolysisGas = yamlNode["lookup gas specific heat capacity"].as<std::vector<real>>();

        _GasEnthalpyInterpolant.set_points(TemperaturesPyrolysisGas, hPyrolysisGas);
        _GasViscosityInterpolant.set_points(TemperaturesPyrolysisGas, MuPyrolysisGas);
        _GasMolarWeightInterpolant.set_points(TemperaturesPyrolysisGas, MPyrolysisGas);
        _GasCpInterpolant.set_points(TemperaturesPyrolysisGas, CpPyrolysisGas);

    } catch (...) {
        error << "Error in General Pyrolysis Material : A required key could not be found in the YAML tree."
              << pyre::journal::endl(__HERE__);
    }

    return; // All done!
}
#endif

void summit::GeneralPyrolysis::Display()
{
    // Check material properties
    Message::Info("General Pyrolysis Parameters:");
    // end of method
    return;
}

summit::real summit::GeneralPyrolysis::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    
    real rhos = q[_INT_DENSITY_BULK];
    real kappa = q[_INT_KAPPA];
    real Cp = q[_INT_CP];

    if (rhos == 0.0){
        rhos = 280.0;
    }

    if (kappa = 0.0){
        kappa = 0.5;
    }

    if (Cp == 0.0){
        Cp = 1400.0;
    }

    // diffusivity
    return kappa / (rhos * Cp);
}

void summit::GeneralPyrolysis::Constitutive(const real* primal0,
                                                           const real* primal,
                                                           const real* Dprimal0,
                                                           const real* Dprimal,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dt,
                                                           const int ndf, // number of components in primal
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{

    if (primal[0] <= 0.0){
        return;
    }

    calculatePartialHeatOfCharring(primal, q);

    real R = 8.3145;

    real T = primal[0];
    real p = primal[1];

    real M = _GasMolarWeightInterpolant(T)/1000.0;
    real dM_dT = _GasMolarWeightInterpolant.deriv(1, T)/1000.0;

    real mu = _GasViscosityInterpolant(T)*0.0001;
    real dmu_dT = _GasViscosityInterpolant.deriv(1, T)*0.0001;

    real phi = _phiVirgin;
    real k = _kVirgin;

    real hg = _GasEnthalpyInterpolant(T)*1.0e3;
    real dhg_dT = _GasEnthalpyInterpolant.deriv(1, T)*1.0e3;

    real rhog = p * M / (R * T);
    real drhog_dT = p / (R * T) * dM_dT - p * M / (R * T * T);
    real drhog_dp = M / (R * T);

    real rhos = q[_INT_DENSITY_SOLID];
    real drhos_dT = q[_INT_DDENSITY_SOLID_DT];

    real rho = rhos + phi * rhog;

    real rhov = _Rho_Total_Virgin;
    real rhoc = _Rho_Total_Char;
    real beta = (rhov - rhos)/(rhov - rhoc);
    real dbeta_dT = - 1.0 / (rhov - rhoc) * drhos_dT;

    real yc = rhoc / rhos * beta;
    real yv = rhov / rhos * (1.0 - beta);
    real yg = phi * rhog / rho;
    real ys = 1.0 - yg;

    real dyc_dT = - rhoc / (rhos * rhos) * drhos_dT * beta + rhoc / rhos * dbeta_dT;
    real dyv_dT = - rhov / (rhos * rhos) * drhos_dT * (1.0 - beta) - rhov / rhos * dbeta_dT;

    real T0 = primal0[0];
    real p0 = primal0[1];

    real M0 = _GasMolarWeightInterpolant(T0)/1000.0;
    real mu0 = _GasViscosityInterpolant(T0)*0.0001;
    real hg0 = _GasEnthalpyInterpolant(T0)*1.0e3;
    real rhog0 = p0 * M0 /(R * T0);
    
    real KappaChar = _KappaCharInterpolant(T);
    real KappaVirgin = _KappaVirgInterpolant(T);

    real dKappaChar_dT = _KappaCharInterpolant.deriv(1, T);
    real dKappaVirgin_dT = _KappaVirgInterpolant.deriv(1, T);

    real Kappa = KappaVirgin * yv + KappaChar * yc;
    real dKappa_dT = dKappaVirgin_dT * yv + dKappaChar_dT * yc + KappaVirgin * dyv_dT + KappaChar * dyc_dT;
    
    for (int dimension = 0; dimension < ndm; dimension++){

        // calculate flux components and store in internal table
        q[_INT_GAS_VELOCITY + dimension] = - k / mu * Dprimal[dimension + ndm];
        q[_INT_DIFFUSIVE_THERMAL_FLUX + dimension] = - Kappa * Dprimal[dimension];
        q[_INT_ADVECTIVE_THERMAL_FLUX + dimension] = - hg0 * rhog0 * k / mu0 * Dprimal0[dimension + ndm];
        q[_INT_DIFFUSIVE_MASS_FLUX + dimension] = rhog * q[_INT_GAS_VELOCITY + dimension];

        // compute the heat flux
        P[dimension + 0 * ndm] = - q[_INT_DIFFUSIVE_THERMAL_FLUX + dimension] - q[_INT_ADVECTIVE_THERMAL_FLUX + dimension];

        // compute the mass flux
        P[dimension + ndm] = - q[_INT_DIFFUSIVE_MASS_FLUX + dimension];

        //compute the tangents if needed
        if(compute_tangents){
            //compute the tangents for the heat equation
            tangent[dimension * ndm * ndf + dimension] = Kappa;
            // tangent[dimension * ndm * ndf + dimension] = hg * phi * rhog * k / mu;
            dPdu[dimension * ndf + 0] = dKappa_dT * Dprimal[dimension];
            // dPdu[dimension * ndf + 0] += dhg_dT * phi * rhog * k / mu * Dprimal[dimension + ndm];
            // dPdu[dimension * ndf + 0] += hg * phi * drhog_dT * k / mu * Dprimal[dimension + ndm];
            // dPdu[dimension * ndf + 0] += - hg * phi * rhog * k / (mu * mu) * dmu_dT * Dprimal[dimension + ndm];
            // dPdu[dimension * ndf + 1] =  drhog_dp * phi * k / mu * Dprimal[dimension + 1 * ndm];

            //compute the tangents for the mass conservation equation
            tangent[(dimension + ndm) * (ndm * ndf) + dimension + ndm] = rhog * k / mu;
            dPdu[(dimension + ndm) * ndf + 0] = drhog_dT * k / mu - rhog * k / (mu * mu) * dmu_dT;
            dPdu[(dimension + ndm) * ndf + 1] = drhog_dp * k / mu * Dprimal[dimension + ndm];
        }
    }

    // this sets the temperature in the internals for transfer to the solid!
    q[_INT_TEMP] = primal[0] - _Tref;

    q[_INT_DENSITY_GAS] = rhog;
    q[_INT_DENSITY_BULK] = rho;

    q[_INT_DEGREE_OF_CHAR] = beta;
    q[_INT_YC] = yc;
    q[_INT_YV] = yv;
    q[_INT_YG] = yg;
    q[_INT_YS] = ys;

    q[_INT_GAS_MOLAR_WEIGHT] = M;
    q[_INT_GAS_VISCOSITY] = mu;
    q[_INT_GAS_ENTHALPY] = hg;

    q[_INT_KAPPA_CHAR] = KappaChar;
    q[_INT_KAPPA_VIRGIN] = KappaVirgin;
    q[_INT_KAPPA] = Kappa;

    q[_INT_PERMEABILITY] = k;
    q[_INT_POROSITY] = phi;
    
    return;
}

summit::real summit::GeneralPyrolysis::capacity(real const* q, const int component) const
{   
    return 0.0;
    
    if (component == 0){
        return q[_INT_DENSITY_BULK] * q[_INT_CP];
    }
    return 1.0;
}

void summit::GeneralPyrolysis::_setInternalVariableMap()
{

    SetLocationInInternalTable("Temperature", _INT_TEMP, 1);
    SetLocationInInternalTable("Solid Density", _INT_DENSITY_SOLID, 1);
    SetLocationInInternalTable("Gas Density", _INT_DENSITY_GAS, 1);
    SetLocationInInternalTable("Bulk Density", _INT_DENSITY_BULK, 1);

    SetLocationInInternalTable("Degree of Char", _INT_DEGREE_OF_CHAR, 1);

    SetLocationInInternalTable("yc", _INT_YC, 1);
    SetLocationInInternalTable("yv", _INT_YV, 1);
    SetLocationInInternalTable("ys", _INT_YS, 1);
    SetLocationInInternalTable("yg", _INT_YG, 1);

    SetLocationInInternalTable("H Char", _INT_H_CHAR, 1);
    SetLocationInInternalTable("H Virgin", _INT_H_VIRGIN, 1);
    SetLocationInInternalTable("Partial Heat of Charring", _INT_PARTIAL_HEAT_CHAR, 1);
    SetLocationInInternalTable("Partial Heat of Charring Tangent", _INT_DPARTIAL_HEAT_CHAR_DT, 1);

    SetLocationInInternalTable("Gas Enthalpy", _INT_GAS_ENTHALPY, 1);
    SetLocationInInternalTable("Gas Viscosity", _INT_GAS_VISCOSITY, 1);
    SetLocationInInternalTable("Gas Molar Weight", _INT_GAS_MOLAR_WEIGHT, 1);

    SetLocationInInternalTable("Kappa Char", _INT_KAPPA_CHAR, 1);
    SetLocationInInternalTable("Kappa Virgin", _INT_KAPPA_VIRGIN, 1);
    SetLocationInInternalTable("Kappa", _INT_KAPPA, 1);

    SetLocationInInternalTable("Cp Char", _INT_CP_CHAR, 1);
    SetLocationInInternalTable("Cp Virgin", _INT_CP_VIRGIN, 1);
    SetLocationInInternalTable("Cp Solid", _INT_CP_SOLID, 1);
    SetLocationInInternalTable("Cp Gas", _INT_CP_GAS, 1);
    SetLocationInInternalTable("Cp", _INT_CP, 1);

    SetLocationInInternalTable("Permeability", _INT_PERMEABILITY, 1);
    SetLocationInInternalTable("Porosity", _INT_POROSITY, 1);

    SetLocationInInternalTable("Diffusive Thermal Flux", _INT_DIFFUSIVE_THERMAL_FLUX, 3);
    SetLocationInInternalTable("Advective Thermal Flux", _INT_ADVECTIVE_THERMAL_FLUX, 3);

    SetLocationInInternalTable("Diffusive Mass Flux", _INT_DIFFUSIVE_MASS_FLUX, 3);
    
    SetLocationInInternalTable("Gas Velocity", _INT_GAS_VELOCITY, 3);

    SetLocationInInternalTable("Decomposition Rate", _INT_DECOMP_RATE, 1);
    SetLocationInInternalTable("Decomposition Rate Tangent", _INT_DDECOMP_RATE_DT, 1);

    SetLocationInInternalTable("Component Solid Density", _INT_COMPONENT_SOLID_DENS, _NUM_OF_REACTIONS);
    SetLocationInInternalTable("Component Decomposition Rate", _INT_COMPONENT_DECOMP_RATE, _NUM_OF_REACTIONS);
    SetLocationInInternalTable("Component Decomposition Rate Tangent", _INT_COMPONENT_DDECOMP_RATE_DT, _NUM_OF_REACTIONS);

    SetLocationInInternalTable("Time", _INT_TIME, 1);

    SetLocationInInternalTable("Coordinates", _INT_COORDINATES, 3);

    // all done
    return;
}

void summit::GeneralPyrolysis::Source(const real* primal0, const real* primal, const real* Dprimal0, const real* Dprimal, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const
{   

    if (primal[0] <= 0.0){
        return;
    }

    // calculate the decomposition rate and corresponding solid density
    calculateDecompositionRate(primal, primal0, q, dt);

    // get solid density from decomposition model
    real rhos = q[_INT_DENSITY_SOLID];
    real drhos_dT = q[_INT_DDENSITY_SOLID_DT];
    real d2Rhos_dT2 = q[_INT_D2DENSITY_SOLID_DT2];
    real ddrhos_dt_dT = q[_INT_DDECOMP_RATE_DT];
    real drhos_dt = q[_INT_DECOMP_RATE];

    // define the universal gas constant
    real R = 8.3145;

    // get primal variables
    real T = primal[0];
    real p = primal[1];

    real T0 = primal0[0];
    real p0 = primal0[1];

    // get molar weight and corresponding derivatives
    real M = _GasMolarWeightInterpolant(T)/1000.0;
    real dM_dT = _GasMolarWeightInterpolant.deriv(1, T)/1000.0;
    real d2M_dT2 = _GasMolarWeightInterpolant.deriv(2, T)/1000.0;

    // get molar weight and corresponding derivatives
    real M0 = _GasMolarWeightInterpolant(T0)/1000.0;

    // get viscosity and corresponding derivative
    real mu = _GasViscosityInterpolant(T)*0.0001;
    real dmu_dT = _GasViscosityInterpolant.deriv(1, T)*0.0001;

    // get gas enthalpy and corresponding derivative
    real hg = _GasEnthalpyInterpolant(T)*1.0e3;
    real dhg_dT = _GasEnthalpyInterpolant.deriv(1, T)*1.0e3;

    // calculate gas density, and corresponding derivatives
    real rhog = p * M / (R * T);
    real drhog_dT = p / (R * T) * dM_dT - p * M / (R * T * T);
    real ddrhog_dT2 = p / R * (d2M_dT2 / T - 2.0 * dM_dT / (T * T) + 2.0 * M / (T * T * T));
    real ddrhog_dp_dT = dM_dT / (R * T) - M / (R * T * T);
    real drhog_dp = M / (R * T);

    // calculate the internal energy of gas
    real eg = hg - R * T / M;
    real deg_dT = dhg_dT - R / M + R * T / (M * M) * dM_dT;
    real deg_dp = 0.0;

    // calculate the degree of char
    real rhov = _Rho_Total_Virgin;
    real rhoc = _Rho_Total_Char;
    real beta = (rhov - rhos)/(rhov - rhoc);
    real dbeta_dT = - 1.0 / (rhov - rhoc) * drhos_dT;

    // get porosity and corresponding derivative
    real phi = _phiVirgin;
    // real phi = _phiVirgin * (1.0 - beta) + _phiChar * beta;
    // real dphi_dT = (_phiChar - _phiVirgin) * dBeta_dT;
    // real d2phi_dT2 = (_phiChar - _phiVirgin) * dBeta_dT;

    // get permeability and corresponding derivative
    real k = _kVirgin;
    // real k = _kVirgin * (1.0 - beta) + _kChar * beta;
    // real dk_dT = (_kChar - _kVirgin) * dBeta_dT;

    // calculate the bulk density and corresponding derivatives 
    real rho = rhos + phi * rhog;
    real drho_dT = drhos_dT + phi * drhog_dT;
    real drho_dp = phi * drhog_dp;

    // calculate the extent of reaction variables
    real yc = rhoc / rhos * beta;
    real yv = rhov / rhos * (1.0 - beta);
    real yg = phi * rhog / rho;
    real ys = 1.0 - yg;
    
    real dyc_dT = - rhoc / (rhos * rhos) * drhos_dT * beta + rhoc / rhos * dbeta_dT;
    real dyv_dT = - rhov / (rhos * rhos) * drhos_dT * (1.0 - beta) - rhov / rhos * dbeta_dT;
    real dyg_dT = phi / rho * (drhog_dT - drho_dT / (rho * rho));
    real dys_dT = - dyg_dT;

    real dyg_dp = phi / rho * drhog_dp - phi * rhog * drho_dp / (rho * rho);
    real dys_dp = - dyg_dp;

    // calculate the partial heat of charring and corresponding derivative
    calculatePartialHeatOfCharring(primal, q);
    real h_bar = q[_INT_PARTIAL_HEAT_CHAR];
    real dh_bar_dT = q[_INT_DPARTIAL_HEAT_CHAR_DT];

    // get the char and virgin sepcific heat capacity at the current temperature
    real CpChar = _CpCharInterpolant(T);
    real CpVirgin = _CpVirgInterpolant(T);

    // calculate the derivatives of the char and virgin specific heat capacity at the current temperature
    real dCpChar_dT = _CpCharInterpolant.deriv(1, T);
    real dCpVirgin_dT = _CpVirgInterpolant.deriv(1, T);

    // calculate the solid material specific heat capacity and corresponding derivative
    real Cps = CpVirgin * yv + CpChar * yc;
    real dCps_dT = dCpVirgin_dT * yv + dCpChar_dT * yc + CpVirgin * dyv_dT + CpChar * dyc_dT;

    // calculate the gas specific heat capacity and corresponding derivative
    real Cpg = _GasCpInterpolant(T)*1000.0;
    real dCpg_dT = _GasCpInterpolant.deriv(1, T)*1000.0;

    // calculate the bulk material specific heat capacity and corresponding derivative
    real Cp = ys * Cps + yg * Cpg;
    real dCp_dT = ys * dCps_dT + yg * dCpg_dT + Cps * dys_dT + Cpg * dyg_dT;
    real dCp_dp = Cpg * dyg_dp + Cps * dys_dp;

    // calculate the source term for the energy balance equation
    f[0] = - rho * Cp * (primal[0] - primal0[0]) / dt[0];
    f[0] -= h_bar * drhos_dt;

    f[0] += phi * rhog * (R / M - R * T / (M * M) * dM_dT) * (primal[0] - primal0[0]) / dt[0];

    f[0] -= eg * phi * drhog_dT * (primal[0] - primal0[0]) / dt[0];
    f[0] -= eg * phi * drhog_dp * (primal[1] - primal0[1]) / dt[0];
    
    // calculate the derivative of the source term for the energy balance equation
    df[0] = - rho * Cp / dt[0]; 
    df[0] -= drho_dT * Cp * (primal[0] - primal0[0]) / dt[0];
    df[0] -= rho * dCp_dT * (primal[0] - primal0[0]) / dt[0];

    df[0] -= dh_bar_dT * drhos_dt;
    df[0] -= h_bar * ddrhos_dt_dT;

    df[0] += phi * rhog * (R / M - R * T / (M * M) * dM_dT) / dt[0];
    df[0] += phi * drhog_dT * (R / M - R * T / (M * M) * dM_dT) * (primal[0] - primal0[0]) / dt[0];
    df[0] += phi * rhog * (- R / (M * M) * dM_dT - R / (M * M) * dM_dT + 2.0 * R * T / (M * M * M) * dM_dT * dM_dT - R * T / (M * M) * d2M_dT2) * (primal[0] - primal0[0]) / dt[0];
    
    df[0] -= eg * phi * drhog_dT / dt[0];
    df[0] -= eg * phi * ddrhog_dT2 * (primal[0] - primal0[0]) / dt[0];
    df[0] -= deg_dT * phi * drhog_dT * (primal[0] - primal0[0]) / dt[0];

    df[0] -= eg * phi * ddrhog_dp_dT * (primal[1] - primal0[1]) / dt[0];
    df[0] -= deg_dT * phi * drhog_dp * (primal[1] - primal0[1]) / dt[0];
    
    df[1] = -drho_dp * Cp * (primal[0] - primal0[0]) / dt[0];
    df[1] -= rho * dCp_dp * (primal[0] - primal0[0]) / dt[0];

    // these terms cause issues for some reason
    // df[1] -= eg * phi * ddrhog_dp_dT * (primal[0] - primal0[0]) / dt[0];
    // df[1] -= eg * phi * drhog_dp / dt[0];
    // df[1] += phi *drhog_dp * (R / M - R * T / (M * M) * dM_dT) * (primal[0] - primal0[0]) / dt[0];

    // calculate the source term for the mass conservation equation
    f[1] = 0.0;
    f[1] -= phi * drhog_dT *(primal[0] - primal0[0]) / dt[0];
    f[1] -= phi * drhog_dp * (primal[1] - primal0[1]) / dt[0];
    f[1] -= drhos_dt;

    // calculate the derivative of the source term for the mass conservation equation
    df[2] = 0.0;
    df[2] -= phi * ddrhog_dT2 * (primal[0] - primal0[0]) / dt[0];
    df[2] -= phi *drhog_dT / dt[0];
    df[2] -= phi * ddrhog_dp_dT * (primal[1] - primal0[1]) / dt[0];
    df[2] -= ddrhos_dt_dT;

    df[3] = 0.0;
    df[3] -= phi * ddrhog_dp_dT * (primal[0] - primal0[0]) / dt[0];
    df[3] -= phi * drhog_dp / dt[0];

    // this sets the temperature in the internals for transfer to the solid!
    q[_INT_TEMP] = primal[0] - _Tref;

    q[_INT_DENSITY_GAS] = rhog;
    q[_INT_DENSITY_BULK] = rho;

    q[_INT_DEGREE_OF_CHAR] = beta;
    q[_INT_YC] = yc;
    q[_INT_YV] = yv;
    q[_INT_YG] = yg;
    q[_INT_YS] = ys;

    q[_INT_GAS_VISCOSITY] = mu;
    q[_INT_GAS_MOLAR_WEIGHT] = M;
    q[_INT_GAS_ENTHALPY] = hg;

    q[_INT_CP] = Cp;
    q[_INT_CP_GAS] = Cpg;
    q[_INT_CP_SOLID] = Cps;
    q[_INT_CP_CHAR] = CpChar;
    q[_INT_CP_VIRGIN] = CpVirgin;

    q[_INT_PERMEABILITY] = k;
    q[_INT_POROSITY] = phi;
    
    return;
}

void summit::GeneralPyrolysis::calculateDecompositionRate(const real * primal, const real * primal0, real * q, real * dt) const
{

    q[_INT_TIME] += *dt;

    real phi = _phiVirgin;

    q[_INT_DENSITY_SOLID] = (1.0 - _Gamma) * _rho_C;
    q[_INT_DECOMP_RATE] = 0.0;
    q[_INT_DDENSITY_SOLID_DT] = 0.0;
    q[_INT_DDECOMP_RATE_DT] = 0.0;
    q[_INT_D2DECOMP_RATE_DT2] = 0.0;

    for (int i = 0; i < 2; ++i){
        real Bi = _B[i];
        real Ei = _E[i];
        real rho_vi = _rhov[i];
        real rho_ci = _rhoc[i];
        real psi = _psi[i];
        real rho_i = q[_INT_COMPONENT_SOLID_DENS+i];
        real T = primal[0];
        real T0 = primal0[0];
        q[_INT_COMPONENT_DECOMP_RATE+i] = 0.0;
        real w1 = 0.0;
        real w2 = 0.0;
        real dw2_dT = 0.0;
        if (T > _Treac[i]){
            w1 = (rho_i-rho_ci)/rho_vi;
            w2 = std::pow(std::pow(w1,1.0-psi) - Bi * (1.0 - psi) * std::exp(-Ei/T)*dt[0],1.0/(1.0-psi));
            q[_INT_COMPONENT_DECOMP_RATE+i] = -Bi*std::exp(-Ei/T)*rho_vi*std::pow(w1, psi);
            q[_INT_COMPONENT_SOLID_DENS+i] = rho_ci + rho_vi * w2;
            dw2_dT = (1.0/(1.0 - psi)) * std::pow(w2,psi) * (- Bi * (1.0 - psi) * std::exp(-Ei/T)*dt[0]) * Ei/T/T;
        } 
        q[_INT_DENSITY_SOLID] += _Gamma * q[_INT_COMPONENT_SOLID_DENS+i];
        q[_INT_DECOMP_RATE] += _Gamma * q[_INT_COMPONENT_DECOMP_RATE+i];
        q[_INT_DDENSITY_SOLID_DT] += _Gamma * dw2_dT * rho_vi;
        q[_INT_DDECOMP_RATE_DT] += _Gamma * q[_INT_COMPONENT_DECOMP_RATE+i] * Ei/T/T;
    }

    q[_INT_DENSITY_SOLID] *= (1.0 - phi);
    q[_INT_DECOMP_RATE] *= (1.0 - phi);
    q[_INT_DDENSITY_SOLID_DT] *= (1.0 - phi);
    q[_INT_DDECOMP_RATE_DT] *= (1.0 - phi);
    q[_INT_D2DENSITY_SOLID_DT2] = 0.0;
    return;
}

void summit::GeneralPyrolysis::calculatePartialHeatOfCharring(const real * primal, real * q) const
{

    real T = primal[0];
    real rho_c = _Rho_Total_Char;
    real rho_v = _Rho_Total_Virgin;

    real hc = _EnthalpiesCharInterpolant(T);
    real hv = _EnthalpiesVirgInterpolant(T);

    q[_INT_H_CHAR] = hc;
    q[_INT_H_VIRGIN] = hv;

    real dhc_dT = _EnthalpiesCharInterpolant.deriv(1, T);
    real dhv_dT = _EnthalpiesVirgInterpolant.deriv(1, T);
    
    q[_INT_PARTIAL_HEAT_CHAR] = (rho_v*hv-rho_c*hc)/(rho_v - rho_c);
    q[_INT_DPARTIAL_HEAT_CHAR_DT] = (rho_v*dhv_dT - rho_c*dhc_dT)/(rho_v - rho_c);

    return;
}


void summit::GeneralPyrolysis::ConvectiveFlux(const real* primal0, const real* primal, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{
    // real T = primal[0];
    // real hg = _GasEnthalpyInterpolant(T);
    // real dhg_dT = _GasEnthalpyInterpolant.deriv(1, T);
    // real mdotx = q[_INT_ADVECTIVE_THERMAL_FLUX + 0] * hg;
    // real mdoty = q[_INT_ADVECTIVE_THERMAL_FLUX + 1] * hg;
    // real mdotz = q[_INT_ADVECTIVE_THERMAL_FLUX + 2] * hg;

    // F[0] = mdotx*hg;
    // F[1] = mdoty*hg;

    // dF[0] = mdotx*dhg_dT;
    // dF[2] = mdoty*dhg_dT;
    
    return;
}

real summit::GeneralPyrolysis::calculateGasEnthalpy(const real T) const{
    real hg = _GasEnthalpyInterpolant(T);
    return hg;
}

real summit::GeneralPyrolysis::calculateSolidCharEnthalpy(const real T) const{
    real hc = _EnthalpiesCharInterpolant(T);
    return hc;
}

real summit::GeneralPyrolysis::calculateDegreeOfChar(const real density) const{

    return (_Rho_Total_Virgin - density) / (_Rho_Total_Virgin - _Rho_Total_Char);
}

real summit::GeneralPyrolysis::calculateEmissivity(const real density) const{

    real beta = calculateDegreeOfChar(density);

    // calculate the extent of reaction variables
    real yc = _Rho_Total_Char / density * beta;
    real yv = 1 - yc;

    // calculate the emissivity
    real emissivity = 0.8*yv + 0.9*yc;

    return emissivity;
}

real summit::GeneralPyrolysis::calculateHeatFlux(const real T, const real density, const real dT_dx) const{

    real beta = calculateDegreeOfChar(density);

    // calculate the extent of reaction variables
    real yc = _Rho_Total_Char / density * beta;
    real yv = 1 - yc;

    real KappaChar = _KappaCharInterpolant(T);
    real KappaVirgin = _KappaVirgInterpolant(T);

    real Kappa = KappaVirgin * yv + KappaChar * yc;

    return - Kappa * dT_dx;
}

real summit::GeneralPyrolysis::calculateMassFlux(const real T, const real p, const real dp_dx) const{

    real R = 8.3145;
    real M = _GasMolarWeightInterpolant(T)/1000.0;
    real mu = _GasViscosityInterpolant(T)*0.0001;
    real phi = _phiVirgin;
    real k = _kVirgin;

    real rhog = p * M / (R * T);
    real gas_velocity = - k / mu * dp_dx;

    return rhog * gas_velocity;
}

int summit::GeneralPyrolysis::number_unknowns() const{
    return 2;
}

real summit::GeneralPyrolysis::bulkModulus(real const* q) const
{
    real kappa = q[_INT_KAPPA];
    if (kappa == 0.0){
        kappa = 3.5;
    }
    return kappa;
}

void summit::GeneralPyrolysis::laxFriedrichStabilization(const real* primal0, const real* primal, real* C, real* dC) const
{   
    C[0] = 0.3;
    return;
}
// end of file