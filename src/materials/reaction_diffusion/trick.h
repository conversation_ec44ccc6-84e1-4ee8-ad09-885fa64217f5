/**
 * @file trick.h
 * <AUTHOR> pickard (<EMAIL>)
 * @brief A material model for complex reaction networks with multiple species
 * 
 * This file implements the TRICK (Thermochemical Reactions In Complex Kinetics) model,
 * which handles multiple chemical species undergoing complex reaction networks with
 * temperature-dependent reaction rates and diffusion.
 */

#ifndef SUMMIT_TRICK_H
#define SUMMIT_TRICK_H

#include "../reaction_diffusion_material.h"

// Number of internal variables:
// the value is used to size the memory allocation for the material Trick
#define TRICK_NUMBER_SPECIES 7 // Trick thesis has 7 species
#define TRICK_NUMBER_REACTIONS 13 // 13 heterogeneous reactions
                                         // 0 homogeneous reactions
#define TRICK_NUMBER_INTERNAL_VARIABLES 13

namespace summit {

/**
 * @brief Implementation of the TRICK model for complex reaction networks
 * 
 * @details The TRICK (Thermochemical Reactions In Complex Kinetics) model implements
 * a framework for simulating complex reaction networks involving multiple chemical species.
 * It is designed to handle:
 * 
 * - Multiple chemical species with different diffusion coefficients
 * - Complex reaction networks with arbitrary stoichiometry
 * - Temperature-dependent reaction rates following Arrhenius kinetics
 * - Coupled heat and mass transfer
 * - Source/sink terms for species production/consumption
 * 
 * The model solves a system of coupled reaction-diffusion equations of the form:
 * 
 * \f[ \frac{\partial C_i}{\partial t} = \nabla \cdot (D_i \nabla C_i) + R_i(C_1, C_2, ..., C_n, T) \f]
 * 
 * where \f$C_i\f$ is the concentration of species i, \f$D_i\f$ is the diffusion coefficient,
 * and \f$R_i\f$ represents the reaction terms that depend on all species concentrations and temperature.
 */
class Trick : public ReactionDiffusionMaterial {
  public:
    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    Trick(const std::string& name);

    /**
     * Destructor
     */
    virtual ~Trick();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    Trick(const Trick&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    Trick& operator=(const Trick&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * Trick constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* concentration,
                              const real* u1,
                              const real* Dconcentration,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const;

    /**
     * set the source term
     */
    void ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;
  
  protected: 

    /**
     * rate constants of the i equations
     */
    std::vector<real> _Ai;

    /**
     * activation constants of the i equations
     */
    std::vector<real> _Ei;

    /**
     * Max gas release of the equations
     */
    std::vector<real> _M0i;

    /**
     * chemical diffusion constants of the j species
     */
    std::vector<real> _Dj;

    /**
     * mechanical diffusion constants of the bulk darcian flow
     */
    real _D;

    

  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
