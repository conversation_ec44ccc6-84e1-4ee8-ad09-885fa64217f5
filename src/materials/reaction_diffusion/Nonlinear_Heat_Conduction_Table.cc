#include <pyre/journal.h>
#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>

#include "Nonlinear_Heat_Conduction_Table.h"
#include "../../mathlib/mathlib.h"
#include "../../io/summit_message.h"

#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    0
// ****************************************************

summit::NonlinearHeatConductionTable::NonlinearHeatConductionTable(const std::string& name)
  : ReactionDiffusionMaterial(name, NONLINEAR_HEAT_CONDUCTION_TABLE_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    _setInternalVariableMap();
}

summit::NonlinearHeatConductionTable::~NonlinearHeatConductionTable() {}

void summit::NonlinearHeatConductionTable::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);

    // all done
    return;
}

#ifdef WITH_YAML_CPP
void summit::NonlinearHeatConductionTable::Load(const YAML::Node &yamlNode) {

    pyre::journal::error_t error("summit.materials.reaction_diffusion.NonlinearHeatConductionTable");
    
    // Read material parameters
    try {

        _rho = yamlNode["density"].as<real>();
        _Tref = yamlNode["reference temperature"].as<real>();
        std::vector<real> Temperatures = yamlNode["lookup solid temperature"].as<std::vector<real>>();
        std::vector<real> Capacities = yamlNode["lookup specific heat capacity"].as<std::vector<real>>();
        std::vector<real> ThermalConductivities = yamlNode["lookup thermal conductivity"].as<std::vector<real>>();

        _KappaInterpolant.set_points(Temperatures, ThermalConductivities);
        _CpInterpolant.set_points(Temperatures, Capacities);

    } catch (...) {
        error << "Error in General Pyrolysis Material : A required key could not be found in the YAML tree."
              << pyre::journal::endl(__HERE__);
    }

    return; // All done!
}
#endif

void summit::NonlinearHeatConductionTable::Display()
{
    // Check material properties
    Message::Info("General Pyrolysis Parameters:");
    // end of method
    return;
}

summit::real summit::NonlinearHeatConductionTable::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
   
    real kappa = q[_INT_KAPPA];
    real Cp = q[_INT_CP];

    if (kappa = 0.0){
        kappa = 0.5;
    }

    if (Cp == 0.0){
        Cp = 1400.0;
    }

    // diffusivity
    return kappa / (_rho * Cp);
}

void summit::NonlinearHeatConductionTable::Constitutive(const real* primal0,
                                                           const real* primal,
                                                           const real* Dprimal0,
                                                           const real* Dprimal,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dt,
                                                           const int ndf, // number of components in primal
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{

    if (primal[0] <= 0.0){
        return;
    }

    real T = primal[0];

    real Kappa = _KappaInterpolant(T);
    real dKappa_dT = _KappaInterpolant.deriv(1, T);
    
    for (int dimension = 0; dimension < ndm; dimension++){

        // calculate flux components and store in internal table
        q[_INT_DIFFUSIVE_THERMAL_FLUX + dimension] = - Kappa * Dprimal[dimension];
        // compute the heat flux
        P[dimension + 0 * ndm] = - q[_INT_DIFFUSIVE_THERMAL_FLUX + dimension];

        //compute the tangents if needed
        if(compute_tangents){
            //compute the tangents for the heat equation
            tangent[dimension * ndm * ndf + dimension] = Kappa;
            dPdu[dimension * ndf + 0] = dKappa_dT * Dprimal[dimension];;
        }
    }

    // this sets the temperature in the internals for transfer to the solid!
    q[_INT_TEMP] = primal[0] - _Tref;
    q[_INT_KAPPA] = Kappa;
    return;
}

summit::real summit::NonlinearHeatConductionTable::capacity(real const* q, const int component) const
{   
    return 0.0;
    
    if (component == 0){
        return _rho * q[_INT_CP];
    }
    return 1.0;
}

void summit::NonlinearHeatConductionTable::_setInternalVariableMap()
{

    SetLocationInInternalTable("Temperature", _INT_TEMP, 1);
    SetLocationInInternalTable("Kappa", _INT_KAPPA, 1);
    SetLocationInInternalTable("Cp", _INT_CP, 1);
    SetLocationInInternalTable("Diffusive Thermal Flux", _INT_DIFFUSIVE_THERMAL_FLUX, 3);
    SetLocationInInternalTable("Coordinates", _INT_COORDINATE, 3);

    // all done
    return;
}

void summit::NonlinearHeatConductionTable::Source(const real* primal0, const real* primal, const real* Dprimal0, const real* Dprimal, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const
{   

    if (primal[0] <= 0.0){
        return;
    }

    // get primal variables
    real T = primal[0];

    // get the char and virgin sepcific heat capacity at the current temperature
    real Cp = _CpInterpolant(T);
    real dCp_dT = _CpInterpolant.deriv(1, T);

    // calculate the source term for the energy balance equation
    f[0] = - _rho * Cp * (primal[0] - primal0[0]) / dt[0];

    df[0] = - _rho * Cp / dt[0]; 
    df[0] += - _rho * dCp_dT * (primal[0] - primal0[0]) / dt[0];
    

    // this sets the temperature in the internals for transfer to the solid!
    q[_INT_TEMP] = primal[0] - _Tref;
    q[_INT_CP] = Cp;
    return;
}

void summit::NonlinearHeatConductionTable::ConvectiveFlux(const real* primal0, const real* primal, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{
    // real hg = q[_INT_GAS_ENTHALPY];
    // real mdotx = q[_INT_MDOT];
    // real mdoty = q[_INT_MDOT+1];
    // real mdotz = q[_INT_MDOT+2];

    // F[0] = mdotx*hg;
    // F[1] = mdoty*hg;
    
    return;
}

int summit::NonlinearHeatConductionTable::number_unknowns() const{
    return 1;
}

real summit::NonlinearHeatConductionTable::bulkModulus(real const* q) const
{
    real kappa = q[_INT_KAPPA];
    if (kappa == 0.0){
        kappa = 3.5;
    }
    return kappa;
}

void summit::NonlinearHeatConductionTable::laxFriedrichStabilization(const real* primal0, const real* primal, real* C, real* dC) const
{   
    C[0] = 0.3;
    return;
}

real summit::NonlinearHeatConductionTable::CalculateAlpha(const real primal) const
{
    return _KappaInterpolant(primal) / _rho / _CpInterpolant(primal);
}


// end of file