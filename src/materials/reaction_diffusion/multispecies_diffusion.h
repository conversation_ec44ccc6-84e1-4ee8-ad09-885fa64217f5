/* 
    <PERSON> (<EMAIL>), 2023
*/

#if defined(WITH_YAML_CPP)
#ifndef SUMMIT_MULTISPECIES_DIFFUSION_H
#define SUMMIT_MULTISPECIES_DIFFUSION_H

#include "../reaction_diffusion_material.h"
#include <pyre/journal.h>

namespace summit {

/**
 * A material implementing ReactionDiffusionMaterial to provide a multispecies interdiffusion 
 * model comprising an arbitrary number of diffusing species, using the <PERSON><PERSON><PERSON><PERSON> approximation.
 */
class MultispeciesDiffusion : public ReactionDiffusionMaterial {
  public:
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    MultispeciesDiffusion(const std::string& name);

    /**
     * Destructor
     */
    virtual ~MultispeciesDiffusion();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    MultispeciesDiffusion(const MultispeciesDiffusion&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    MultispeciesDiffusion& operator=(const MultispeciesDiffusion&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    /**
     * Method to load material properties from a YAML tree
     * @param[in] node the YAML tree for the specific material
     */
    void Load(const YAML::Node &node) override;

    /**
      * Method to compute the diffusivity for the critical time step
      * @param[in] Fn a pointer of real
      * @param[in] internal a pointer to the internal variables at the quadrature point
      * @param[in] ndm number of spatial dimensions
      * @param[in] component the index of the primal field that the diffusivity returned 
      *                      corresponds to (NOT IMPLEMENTED IN SYSTEM)
      * @return The diffusivity
      */
    real diffusivity(const real* Fn, const real* internal, const int ndm, const int component) const override;

    /**
     * MultispeciesDiffusion constitutive update (all tensors are ROW MAJOR)
     * @param[in]  u0 the interpolation of primal field u0 at quadrature point
     * @param[in]  u1 the interpolation of primal field u1 at quadrature point
     * @param[in]  Du0 the gradient of the primal field
     * @param[in]  Du1 the gradient of the primal field
     * @param[out] F the diffusive (and convective?) flux
     * @param[in]  internal a pointer to the internal variables at the quadrature point
     * @param[out] dFdDu the Jacobian tangent dD/(du/dx)
     * @param[out] dFdu  the 
     * @param[in]  dtime the timestep
     * @param[in]  ndf number of components in the primal field
     * @param[in]  ndm number of spatial dimensions
     * @param[in]  compute_tangents the flag for computing the tangent
     * @param[in]  artVisc_interface_activate the flag for artificial viscosity
     */
    void Constitutive(const real* u0,
                      const real* u1,
                      const real* Du0,
                      const real* Du1,
                      real* F,
                      real* internal,
                      real* dFdDu,
                      real* dFdu,
                      real dtime,
                      const int ndf,
                      const int ndm,
                      bool compute_tangents = false,
                      bool artVisc_interface_activate = true) const override;

    /**
     * Set the chemical source term + transient term (all tensors are ROW MAJOR)
     * @param[in]  u0 the interpolation of primal field u0 at quadrature point
     * @param[in]  u1 the interpolation of primal field u1 at quadrature point
     * @param[in]  internal a pointer to the internal variables at the quadrature point
     * @param[in]  dtime the timestep
     * @param[out] r the transient term + the chemical source term
     * @param[out] drdu the derivative of the transient term w.r.t. the primal state
     * @param[in]  ndf number of components in the primal field
     */
    void Source(const real* u0,
                const real* u1,
                const real* Du0,
                const real* Du1,
                real* internal,
                real* dtime,
                real* r,
                real* drdu,
                real* drdGrad,
                size_t ndm,
                size_t ndf) const override;

    /**
     * Set the convective flux (used for DG upwinding) (all tensors are ROW MAJOR)
     * @param[in]  u0 the interpolation of primal field u0 at quadrature point
     * @param[in]  u1 the interpolation of primal field u1 at quadrature point
     * @param[in]  internal a pointer to the internal variables at the quadrature point
     * @param[in]  dtime the timestep
     * @param[out] F the convective flux
     * @param[out] dF the derivative of the convective w.r.t. the primal state
     * @param[in]  ndf number of components in the primal field
     * @param[in]  ndm number of spatial dimensions
     */
    void ConvectiveFlux(const real* u0,
                        const real* u1,
                        real* internal, 
                        real* dtime, 
                        real* F, 
                        real* dF, 
                        size_t ndf, 
                        size_t ndm) const override;

    /**
     * Access the coefficient of the transient term, for explicit time integration stabilization
     * @param[in] internal a pointer to the internal variables at the quadrature point
     * @param[in] component The component of the primal vector to return the capacity for
     * @return The maximum expected capacity (or a conservative estimate)
     */
    real capacity(real const* internal, const int component = 0) const override;

    /**
     * Compute the analog to the bulk modulus in the RD problem (important for DG calculations)
     * @param[in] internal a pointer to the internal variables at the quadrature point
     * @return The maximum expected "bulk modulus" (or a conservative estimate)
     */
    real bulkModulus(real const* internal) const override;

    /**
     * Compute the number of unknowns in the primal field vector
     * @return The number of unknowns in the primal field vector
    */
    int number_unknowns() const override;
  
  protected: 

    /**
     * Choice of flux type: "Fickian", "Blottner-unconstrained", "Blottner-constrained"
     */
    std::string _type;

    /**
     * Number of gas species
     */
    int _nsp;

    /**
     * Vector of species names
     */
    std::vector<std::string> _names;

    /**
     * Matrix of binary diffusion coefficients
     */
    std::vector<std::vector<real>> _Dij;


    /**
     * Vector of molar masses (kg/kmol)
     */
    std::vector<real> _Mi;

    /**
     * Fixed pressure and temperature
     */
    real _p, _T;

    /** 
     * Universal gas constant (J/mol/K)
    */
    const real _R = 8.3145;

  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();

    /**
     * Get the gas mean molar mass corresponding to primal state u
     * @param[in] Y the interpolation of primal field Y at quadrature point
     * @return The mixture-averaged molar mass of the gas phase (kg/kmol)
     */
    virtual real Mbar(real const* Y) const;
    
};
}  // namespace summit

#endif
#endif