/*
    <PERSON> (<EMAIL>), 2024
*/

#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <fstream>
#include <iostream>
#include <algorithm>
#include <iterator>
#include <iterator>
#include <string>
#include <vector>
#include <array>
#include <tuple>
#include <pyre/journal.h>
#include "../../mathlib/mathlib.h"
#include "../../restart/checkpoint.h"
#include "../../mathlib/mathvec.h"
#include "../../mathlib/mathmat.h"
#include "multispecies_dissolved.h"

#define NUM_INT_VARS 5 // Does not include solid species
#define VAR_PHI 0      // Measure of porosity
#define VAR_TEMP 1     // Temperature
#define VAR_EXTENT 2   // Extent of reaction (volumetric)
#define VAR_Y 3        // Quadrature point y-coordinate
#define VAR_RHO 4      // Measure of porosity

summit::MultispeciesDissolved::MultispeciesDissolved(const std::string& name) 
    : ReactionDiffusionMaterial(name, _undefInt), _gas_species(), _solid_species() { }

summit::MultispeciesDissolved::~MultispeciesDissolved() { }

void summit::MultispeciesDissolved::Load(const std::string& filename, const std::string& line) {
    pyre::journal::firewall_t error("summit.materials.multispecies_dissolved");
    error << "Died in MultispeciesDissolved Load()." << pyre::journal::newline
          << "This material does not support the .dat Load() method."
          << pyre::journal::endl(__HERE__); 
    return; // Done!
}

#ifdef WITH_YAML_CPP
void summit::MultispeciesDissolved::Load(const YAML::Node &yamlNode) {
    
    pyre::journal::error_t error("summit.materials.elastic.multispecies_dissolved");
    std::string err = "Error in MultispeciesDissolved Material YAML reader: ";

    try { // Read material parameters
        YAML::Node solidNode  = yamlNode["solid phases"].as<YAML::Node>();
        _gas_species          = yamlNode["dissolved species"].as<std::vector<std::string>>();
        _activationEnthalpies = yamlNode["activation enthalpy matrix"].as<std::vector<std::vector<real>>>();
        _Dij              = yamlNode["diffusivites matrix"].as<std::vector<std::vector<real>>>();
        _solid_species    = solidNode["names"].as<std::vector<std::string>>();
        _conductivities   = solidNode["thermal conductivities"].as<std::vector<real>>();
        _heatCapacities   = solidNode["heat capacities"].as<std::vector<real>>();
        _solidDensities   = solidNode["solid densities"].as<std::vector<real>>();
        _solidMolarMasses = solidNode["molar masses"].as<std::vector<real>>();
    } catch (...) {
        error << err << "A required key was not provided or cannot be cast to the right type."
              << pyre::journal::endl(__HERE__);
    }

    _nsp_gas   = _gas_species.size();
    _nsp_solid = _solid_species.size();
    _nph = _nsp_solid + 1; // Solid phases + gas phase

    // Only two solid species currently supported
    if (_nsp_solid != 2)
        error << err << "Only two solid species are currently supported."
              << pyre::journal::endl(__HERE__);
    
    // Check that the number of diffusivity freq. factors & enthalpies matches the number of species
    if (_Dij.size() != _nsp_solid || _Dij[0].size() != _nsp_gas|| 
        _activationEnthalpies.size() != _nsp_solid || _activationEnthalpies[0].size() != _nsp_gas)
        error << err << "The size of the diffusivity matrices must be nsp_solid x nsp_gas."
              << pyre::journal::endl(__HERE__);

    // Check that the length of thermal conductivities and heat capacities are _nsp_solid
    if (_conductivities.size() != _nsp_solid || _heatCapacities.size() != _nsp_solid)
        error << err << "The size of the thermal conductivities and heat capacities must be nsp_solid."
              << pyre::journal::endl(__HERE__);

    // Set internal variable fields
    _nInt = NUM_INT_VARS + _nph; // The +1 is for the gas phase
    this->_setInternalVariableMap();

    return; // Done!
}
#endif

void summit::MultispeciesDissolved::_setInternalVariableMap() {
    this->SetLocationInInternalTable("phi", VAR_PHI, 1);
    this->SetLocationInInternalTable("Temperature", VAR_TEMP, 1);
    this->SetLocationInInternalTable("ExtentReaction", VAR_EXTENT, 1);
    this->SetLocationInInternalTable("y", VAR_Y, 1);
    this->SetLocationInInternalTable("rho", VAR_RHO, 1);
    this->SetLocationInInternalTable("phases", NUM_INT_VARS, _nsp_solid + 1);
    return; // All done
}

void summit::MultispeciesDissolved::Display() {
    pyre::journal::info_t info("summit.materials.multispecies_dissolved");
    info << "------------------------------------------------------------"
         << pyre::journal::newline
         << "Transport Model: Multispecies diffusion of dissolved species in a multiphase medium"
         << pyre::journal::newline
         << "                with " << _nsp_gas << " dissolved and " << _nsp_solid << " solid species."
         << pyre::journal::newline << pyre::journal::newline
         << "Diffusion coefficient matrix (m^2/s):" << pyre::journal::newline << "\t";

    // Diffusivities matrix and table of gas and solid species
    for (int l = 0; l < _nsp_gas; l++) info << "\t" << _gas_species[l];
    info << pyre::journal::newline;
    for (int k = 0; k < _nsp_solid; k++) {
        info << "\t" << _solid_species[k] << "\t";
        for (int l = 0; l < _nsp_gas; l++)
            info << _Dij[k][l] << "\t";
        info << pyre::journal::newline;
    }
    // Activation enthalpies matrix and table of gas and solid species
    info << pyre::journal::newline << "Activation Enthalpies Matrix (J/mol):" 
         << pyre::journal::newline << "\t";
    for (int l = 0; l < _nsp_gas; l++) info << "\t" << _gas_species[l];
    info << pyre::journal::newline;
    for (int k = 0; k < _nsp_solid; k++) {
        info << "\t" << _solid_species[k] << "\t";
        for (int l = 0; l < _nsp_gas; l++)
            info << _activationEnthalpies[k][l] << "\t";
        info << pyre::journal::newline;
    }
    // Thermal conductivities, heat capacities, and solid densities
    info << pyre::journal::newline << "Solid Phase Properties:" << pyre::journal::newline
         << " - Thermal Conductivities (W/m/K): [";
    for (int k = 0; k < _nsp_solid; k++)
        info << _solid_species[k] << ": " << _conductivities[k] << "; ";
    info << "]" << pyre::journal::newline;
    info << " - Heat Capacities (J/kg/K): [";
    for (int k = 0; k < _nsp_solid; k++)
        info << _solid_species[k] << ": " << _heatCapacities[k] << "; ";
    info << "]" << pyre::journal::newline;
    info << " - Densities (kg/m^3): [";
    for (int k = 0; k < _nsp_solid; k++)
        info << _solid_species[k] << ": " << _solidDensities[k] << "; ";
    info << "]" << pyre::journal::newline;
    
    // Primal state vector reference map
    info << pyre::journal::newline << "Primal state vector reference map: [Temperature";
    for (int l = 0; l < _nsp_gas; l++) info << ", " << _gas_species[l];
    info << "]" << pyre::journal::newline;

    // Phase internal vector reference map
    info << pyre::journal::newline << "Phase internal vector reference map: [gasphase";
    for (int l = 0; l < _nsp_solid; l++) info << ", " << _solid_species[l];
    info << "]" << pyre::journal::newline;
    
    info << "------------------------------------------------------------"
         << pyre::journal::endl;

    return; // Done!
}

summit::real summit::MultispeciesDissolved::diffusivity(const real* Fn,
                                            const real* q,
                                            const int ndm,
                                            const int component) const {
    return 0;
}

void summit::MultispeciesDissolved::Constitutive(const real* u0,
                                                 const real* u1,
                                                 const real* gradu0,
                                                 const real* gradu1,
                                                 real* F,
                                                 real* internal,
                                                 real* dFdgradu,
                                                 real* dFdu,
                                                 real  dt,
                                                 const int ndf,
                                                 const int ndm, // Number of dimensions
                                                 bool compute_tangents,
                                                 bool artVisc_interface_activate) const {
    
    const real T      = u0[0]; // K (Explicit temperature coupling)
    const real phi    = internal[VAR_PHI];
    const real extent = this->extentOfReaction(internal);
    const int  dof    = this->number_unknowns();

    // Fill
    if (compute_tangents) {
        std::fill(dFdu, dFdu + dof * dof, 0.);
        std::fill(dFdgradu, dFdgradu + dof * ndm * ndf, 0.);
    }

    // Thermal conductivity --- volume-based rule of mixtures
    real kappa = _conductivities[0] * (1 - extent) + _conductivities[1] * extent; // W/m^2/K

    // Molecular Diffusion --- volume-based rule of mixtures
    real D0[_nsp_gas];
    for (int i = 0; i < _nsp_gas; i++)
        D0[i] = _Dij[0][i] * (1 - extent) + _Dij[1][i] * extent; // m^2/s

    // Activation Enthalpies --- volume-based rule of mixtures
    real deltaH[_nsp_gas];
    for (int i = 0; i < _nsp_gas; i++)
        deltaH[i] = _activationEnthalpies[0][i] * (1 - extent) 
                        + _activationEnthalpies[1][i] * extent; // m^2/s

    // Loop over primal fields
    for (int i = 0; i < dof; i++) {
        real alpha_i; // Diffusivity
        if (i == 0)   // Energy conservation
            alpha_i = kappa;
        else          // Species mass conservation
            alpha_i = D0[i-1] * std::sqrt(T) * std::exp(-deltaH[i-1] / _R / T);
        
        // Loop over spatial dimensions
        for (int dim = 0; dim < ndm; dim++) {
            int id = dim + i * ndm;
            F[id] = alpha_i * gradu1[id];
            
            // non-zero dFdDY tangent
            if (compute_tangents) {
                int idjk = id * (ndm * dof) + id; // Diagonal index of tangent 4-tensor
                dFdgradu[idjk] = alpha_i;
            }
        }
    }

    return; // Done!
}

summit::real summit::MultispeciesDissolved::capacity(real const* internal, const int component) const {
    return 0.; // Done!
}

void summit::MultispeciesDissolved::Source(const real* u0raw,
                                           const real* u1raw,
                                           const real* Du0raw,
                                           const real* Du1raw,
                                           real* internal,
                                           real* dtime,
                                           real* r,
                                           real* drdu,
                                           real* drdGrad,
                                           size_t ndm,
                                           size_t ndf) const {
    
    // Fix states (if negative make zero)
    int dof = this->number_unknowns();
    real u0[dof], u1[dof];
    this->normalizeState(u0raw, u0);
    this->normalizeState(u1raw, u1);
    
    // Gather or compute constants & arrays of constants at current timestep
    const real T0 = u0[0]; // K
    const real T1 = u1[0]; // K
    const real extent = this->extentOfReaction(internal);
    const real dt = *(dtime);

    // Solid phase density and heat capacity --- volume-based rule of mixtures
    real rho_s = _solidDensities[0] * (1 - extent) + _solidDensities[1] * extent; // kg/m^3
    real Cp    = _heatCapacities[0] * (1 - extent) + _heatCapacities[1] * extent; // J/kg/K

    // Compute thermal transient terms and tangent
    real dTdt = rho_s * Cp * (T1 - T0) / dt; // W/m^3
    real d2TdTdt = rho_s * Cp / dt;          // W/m^3/K

    // Assemble and fill
    std::fill(drdu, drdu + (dof * dof), 0.);
    for (int i = 0; i < dof; i++) {
        // Source
        if (i == 0) r[0] = - dTdt;
        else r[i] = - (u1[i] - u0[i]) / dt;

        // Tangent
        for (int j = 0; j < dof; j++)
            if (i == j)
                if (i == 0) drdu[0] = - d2TdTdt;
                else drdu[i + dof * j] = - 1 / dt;
    }

    // Store variables in internal table for transfer to mechanics system
    internal[VAR_TEMP]   = u1[0];
    internal[VAR_RHO]    = rho_s;
    internal[VAR_EXTENT] = extent;

    return; // Done!
}

void summit::MultispeciesDissolved::ConvectiveFlux(const real* u0,
                                                   const real* u1,
                                                   real* internal,
                                                   real* dtime,
                                                   real* F,
                                                   real* dF,
                                                   size_t ndf,
                                                   size_t ndm) const {
    return; // No advection
}

int summit::MultispeciesDissolved::number_unknowns() const {
    return _nsp_gas + 1; // Gas concentration primals + temperature
}

real summit::MultispeciesDissolved::bulkModulus(real const* internal) const {
    real kappa_max = *std::max_element(_conductivities.begin(), _conductivities.end());
    real rho_min   = *std::min_element(_solidDensities.begin(), _solidDensities.end());
    real Cp_min    = *std::min_element(_heatCapacities.begin(), _heatCapacities.end());
    return kappa_max / rho_min / Cp_min; // Done!
}

void summit::MultispeciesDissolved::normalizeState(real const* u, real* unorm) const {
    int dof = this->number_unknowns();
    std::copy(u, u + dof, unorm);
    for (int i = 0; i < dof; i++)
        if (u[i] < 0.) unorm[i] = 0.;
}

real summit::MultispeciesDissolved::extentOfReaction(real const* internal) const {
    // Condensed phase molar volume
    std::vector<real> f(_nsp_solid, 0.);
    for (int i = 0; i < _nsp_solid; i++) {
        real x_i = internal[NUM_INT_VARS + 1 + i];
        f[i] = x_i * _solidMolarMasses[i] / 1.e3 / _solidDensities[i]; // m^3/mol
    }
    real solid_vol = std::accumulate(f.begin(), f.end(), 0.); // m^3/kmol

    // Check if solid has been entirely consumed
    if (solid_vol < 1e-9) return internal[VAR_EXTENT]; // Return extent of reaction from previous timestep
    
    std::transform(f.begin(), f.end(), f.begin(), [solid_vol](real &c) {return c / solid_vol;});
    return f[1]; // Done!
}

std::vector<std::string> summit::MultispeciesDissolved::getSolidNames() const {
    return _solid_species; // Done!
}

std::vector<std::string> summit::MultispeciesDissolved::getDissolvedNames() const {
    return _gas_species; // Done!
}

int summit::MultispeciesDissolved::name2index(const std::string name) const {
    // Check if requesting temperature
    if (name == "temp" || name == "Temp" || name == "Temperature" || name == "temperature")
        return 0;

    // Check if is the gas phase
    auto pos = std::find(_gas_species.begin(), _gas_species.end(), name);
    if (pos != _gas_species.end()) return std::distance(_gas_species.begin(), pos) + 1;
    return -1; // Not found
}

std::string summit::MultispeciesDissolved::index2name(const int i) const {
    if (i == 0) return "Temperature";
    if (i < _nsp_gas + 1) return _gas_species[i - 1];
    return "";
}

// end of file
